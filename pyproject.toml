[project]
name = "interchat"
version = "5.1.0"
description = "InterChat is a discord bot that lets you talk across servers"
readme = "README.md"
license = { file = "LICENCE" }
requires-python = ">=3.12"
dependencies = [
  "aiofiles",
  "aiohttp",
  "asyncpg",
  "cogwatch",
  "discord",
  "dotenv",
  "jinja2",
  "jishaku",
  "playwright",
  "cuid2",
  "psutil",
  "pyyaml",
  "redis",
  "sentry-sdk",
  "rich",
  "unidecode>=1.4.0",
  "psycopg-binary>=3.2.1",
]

[dependency-groups]
dev = ["alembic", "psycopg2-binary", "pyright", "ruff>=0.12.11", "watchdog"]

[tool.ruff]
line-length = 120

[tool.ruff.format]
quote-style = "single"
