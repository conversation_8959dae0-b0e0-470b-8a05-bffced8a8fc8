# Claude AI Guidelines for InterChat Discord Bot

This document serves as a comprehensive guide for <PERSON> when working on the InterChat Discord bot codebase. It contains essential information about project structure, conventions, patterns, and best practices.

## Project Overview

**InterChat** is a Python Discord bot that enables cross-server communication through "hubs" - channels that broadcast messages across multiple Discord servers. The bot facilitates community building, moderation, and communication management across the Discord ecosystem.

- **Language**: Python 3.12+
- **Framework**: discord.py (async)
- **Database**: PostgreSQL with SQLAlchemy (async)
- **Cache**: Redis/Dragonfly
- **Package Manager**: uv
- **Version**: 5.1.0

## Tech Stack & Dependencies

### Core Dependencies
- `discord.py` - Discord API wrapper
- `asyncpg` - Async PostgreSQL driver
- `sqlalchemy` - ORM with async support
- `alembic` - Database migrations
- `redis` - Caching and rate limiting
- `aiohttp` - HTTP client for external APIs
- `jishaku` - Bot debugging and management
- `cogwatch` - Hot-reloading for development

### Development Tools
- `ruff` - Linting and formatting (line length: 120, single quotes)
- `pyright` - Type checking
- `rich` - Enhanced console logging (development only)
- `sentry-sdk` - Error tracking (production)

### Optional Dependencies
- `playwright` - Browser automation for some features
- `psutil` - System monitoring
- `cuid2` - Unique ID generation

## Project Structure

```
├── main.py                 # Bot entry point, main Bot class
├── cogs/                   # Discord bot commands and event handlers
│   ├── app_commands/       # Slash commands
│   ├── modules/           # Core bot features (hubs, moderation)
│   ├── events/            # Event handlers (message processing, etc.)
│   ├── tasks/             # Background tasks
│   └── developer/         # Developer/admin commands
├── utils/                 # Utility modules and shared code
│   ├── modules/core/      # Core systems (database, i18n, rate limiting)
│   ├── modules/services/  # Business logic services
│   ├── modules/ui/        # Discord UI components and views
│   ├── modules/emojis/    # Emoji management
│   └── constants.py       # Configuration constants
├── alembic/              # Database migrations
├── locales/              # Internationalization files (YAML)
├── data/                 # Static data (emoji links, locale mappings)
└── dev/                  # Docker compose files for development
```

## Code Conventions & Patterns

### Code Style
- **Formatting**: Use `ruff format .` (single quotes, 120 line length)
- **Linting**: Use `ruff check . --fix` for auto-fixes
- **Type Hints**: Use modern Python type hints (`str | None` instead of `Union[str, None]`)
- **String Literals**: Single quotes (`'text'`) preferred
- **Comments**: Minimal comments, prefer self-documenting code
- **Imports**: Group imports: stdlib, third-party, local modules

### Class Patterns
- **Bot Class**: Inherits from `commands.AutoShardedBot`
- **Cogs**: Inherit from `CogBase` in `utils.modules.common.cogs`
- **Database Models**: Use SQLAlchemy declarative base from `utils.modules.core.db.models`
- **UI Views**: Extend `discord.ui.View` for interactive components

### Async Patterns
- All database operations are async (`async with bot.db.get_session()`)
- Use context managers for database sessions
- HTTP requests use aiohttp client session
- Discord operations are async by nature

### Error Handling
- Use structured logging via `utils.constants.logger`
- Development: Rich console output with colors
- Production: Plain text with Sentry integration
- Graceful error handling with user-friendly messages

### Configuration Management
- Environment variables in `.env` file (never commit)
- Constants class in `utils.constants.py`
- Type conversion helpers (`_to_bool()`)
- Production vs development environment handling

## Database Guidelines

### Models & ORM
- **Location**: `utils/modules/core/db/models.py`
- **Pattern**: SQLAlchemy declarative models with async support
- **Relationships**: Use `relationship()` with proper foreign keys
- **Migrations**: Alembic for schema changes (`alembic revision --autogenerate`)

### Database Sessions
```python
async with self.bot.db.get_session() as session:
    # Database operations here
    stmt = select(Model).where(Model.id == value)
    result = await session.execute(stmt)
    await session.commit()  # Only for writes
```

### Common Patterns
- Use `select()` for queries, not raw SQL
- Always use parameterized queries
- Use context managers for session management
- Prefer scalars() for single values, scalars().all() for lists

## Discord Integration Patterns

### Command Structure
- **Slash Commands**: In `cogs/app_commands/`
- **Context Menus**: Use `@context_menu()` decorator
- **Hybrid Commands**: Support both prefix and slash
- **Error Handling**: Defer responses, use ephemeral for errors

### Internationalization (i18n)
- **Locale Files**: YAML files in `locales/{language_code}/`
- **Usage**: `t('key.path', locale=user_locale)` from `utils.modules.core.i18n`
- **Supported Languages**: 70+ languages in `data/localeMap.json`
- **Fallback**: English (`en`) if translation missing

### UI Components
- **Embeds**: Use `discord.Embed` with consistent branding
- **Views**: Interactive components (buttons, selects) in `utils.modules.ui/`
- **Modals**: Custom forms for user input
- **Colors**: Use `constants.color` (0x9172D8) for consistency

## Development Workflow

### Environment Setup
1. **Dependencies**: `uv sync --frozen` (never cancel, ~30 seconds)
2. **Environment**: Copy `.env.example` to `.env`, configure values
3. **Services**: Start PostgreSQL and Redis/Dragonfly via Docker
4. **Migrations**: `uv run alembic upgrade head`

### Code Quality Checks
```bash
# Format code (required before commits)
uv run ruff format .

# Lint and fix issues (required before commits)
uv run ruff check . --fix

# Type checking
uv run pyright

# Test startup (should show locale error, not import errors)
timeout 10 uv run python main.py
```

### Database Changes
```bash
# Create migration after model changes
uv run alembic revision --autogenerate -m "Description"

# Apply migrations
uv run alembic upgrade head
```

### Development Server
```bash
# Start with hot-reloading
uv run python main.py

# Bot features:
# - Loads all cogs automatically
# - Cogwatch for hot-reloading
# - Rich console logging (dev mode)
# - "In development" presence
```

## Key Features & Modules

### Hub System
- **Core Feature**: Cross-server message broadcasting
- **Models**: Hub, Connection, Message in database
- **Events**: Hub message processing, moderation sync
- **Commands**: Hub creation, management, settings

### Moderation System
- **Features**: Cross-server bans, reports, appeals
- **Models**: HubReport, Appeal, User moderation data
- **UI**: Report action views, appeal processing
- **Integration**: Webhook-based message deletion

### Rate Limiting
- **Implementation**: Redis-based rate limiting
- **Patterns**: Per-user, per-command limits
- **Configuration**: Configurable limits in constants
- **Bypass**: Staff/admin bypass capabilities

### Profanity Filtering
- **Service**: `utils.modules.core.profanity/`
- **Features**: Pattern matching, bypass detection
- **Actions**: Configurable responses (warn, delete, ban)
- **Customization**: Hub-specific filter settings

## Testing & Validation

### Manual Testing Requirements
- **Hub Creation**: Test `/hub create` command
- **Message Broadcasting**: Send messages across connected channels
- **Moderation**: Test ban/timeout sync across hubs
- **Commands**: Verify slash command registration and responses
- **Internationalization**: Test commands in different locales

### Performance Expectations
- **Startup Time**: ~10-30 seconds (depends on cog count)
- **Command Response**: <2 seconds for most operations
- **Database Queries**: Use connection pooling (20 connections)
- **Memory Usage**: Monitor with `psutil` integration

## Security & Best Practices

### Security Considerations
- **Never log or expose secrets** (tokens, database URLs)
- **Validate user input** in all commands and modals
- **Use parameterized queries** to prevent SQL injection
- **Rate limiting** to prevent abuse
- **Permission checks** for administrative commands

### Performance Best Practices
- **Connection pooling** for database (configured in Database class)
- **Redis connection pooling** with health checks
- **HTTP session reuse** via aiohttp ClientSession
- **Efficient queries** with proper indexing
- **Caching** for frequently accessed data

### Error Handling
- **Graceful degradation** when services unavailable
- **User-friendly error messages** via embeds
- **Structured logging** for debugging
- **Retry logic** for transient failures
- **Sentry integration** for production error tracking

## Common Issues & Solutions

### Startup Issues
- **Missing locale files**: Causes bot startup failure
- **Invalid Discord token**: Check `.env` configuration
- **Database connection**: Verify PostgreSQL is running and accessible
- **Redis connection**: Ensure Redis/Dragonfly is available

### Development Issues
- **Import errors**: Run `uv sync --frozen` to fix dependencies
- **Lint failures**: Use `ruff format . && ruff check . --fix`
- **Migration conflicts**: Resolve with `alembic` commands
- **Hot-reload failures**: Check cogwatch is working, restart if needed

### Performance Issues
- **Slow queries**: Check database indexes and query patterns
- **Memory leaks**: Monitor with `psutil`, check for unclosed sessions
- **Rate limiting**: Adjust limits in constants if needed
- **Connection timeouts**: Review pool settings and timeouts

## File-Specific Guidelines

### main.py
- **Bot class definition**: Core bot initialization and event handlers
- **Startup logic**: Database initialization, cog loading, service setup
- **Error handling**: Retry logic for connection failures
- **Lifecycle management**: Proper cleanup in `close()` method

### utils/constants.py
- **Configuration hub**: All environment variables and settings
- **Type conversion**: Helper functions for env var parsing
- **Logging setup**: Different handlers for dev/production
- **Redis client**: Global client instance with connection pooling

### cogs/
- **Modular commands**: Each cog handles specific functionality
- **Event handlers**: Process Discord events (messages, joins, etc.)
- **Error handling**: Consistent error responses and logging
- **Permissions**: Check user permissions before command execution

### utils/modules/core/db/
- **Database layer**: Models, connection management, migrations
- **Session management**: Async context managers for database operations
- **Model relationships**: Proper foreign keys and relationships
- **Migration scripts**: Alembic-generated database schema changes

This document should be referenced first when working on the InterChat codebase. It provides the essential context and patterns needed to maintain code quality and consistency.