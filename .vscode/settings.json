{"files.associations": {"*.py": "python"}, "python.analysis.autoIndent": true, "python.analysis.autoImportCompletions": true, "python.analysis.diagnosticMode": "workspace", "python.analysis.logLevel": "Error", "python.terminal.useEnvFile": true, "editor.detectIndentation": true, "editor.formatOnSave": true, "editor.codeLens": true, "editor.suggestSelection": "first", "editor.fontLigatures": true, "python-envs.defaultEnvManager": "ms-python.python:venv", "python-envs.pythonProjects": [], "conventionalCommits.scopes": ["report", "broadcast", "logging"]}