# I approve - Bread

FROM python:3.12-slim

ARG BUILDKIT_INLINE_CACHE=1
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PLAYWRIGHT_BROWSERS_PATH=/ms-playwright \
    PLAYWRIGHT_SKIP_VALIDATE_HOST_REQUIREMENTS=true

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    ca-certificates wget curl unzip gnupg fonts-liberation \
    libasound2 libatk-bridge2.0-0 libatk1.0-0 libcups2 libdbus-1-3 \
    libdrm2 libxkbcommon0 libgtk-3-0 libnss3 libxcomposite1 libxdamage1 \
    libxrandr2 libgbm1 libpango-1.0-0 libpangocairo-1.0-0 libatspi2.0-0 \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

RUN --mount=type=cache,target=/root/.cache/pip \
    pip install --upgrade pip setuptools wheel && \
    pip install uv

WORKDIR /app

COPY pyproject.toml uv.lock* ./
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen --no-dev --no-install-project

RUN --mount=type=cache,target=/root/.cache/playwright \
    uv run playwright install chromium

# Create a non-root user and set up directories for cache owned by that user
RUN groupadd --gid 1000 interchatuser && \
    useradd --uid 1000 --gid interchatuser --create-home --shell /bin/bash interchatuser && \
    mkdir -p /home/<USER>/.cache/playwright /home/<USER>/.cache/uv && \
    chown -R interchatuser:interchatuser /home/<USER>

# Copy application code
COPY --chown=interchatuser:interchatuser . .

USER interchatuser
ENV HOME=/home/<USER>

HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 0

CMD ["uv", "run", "python", "-m", "main"]
