from datetime import datetime
from typing import TYPE_CHECKING, Optional, Union

from sqlalchemy import case, func, select
from sqlalchemy.dialects.postgresql import insert as pg_insert

from utils.modules.core.db.models import User
from discord.ext import commands

if TYPE_CHECKING:
    import discord
    from sqlalchemy.ext.asyncio import AsyncSession

# Type alias for Discord interaction/context
DiscordInteraction = Union[commands.Context, 'discord.Interaction']


def get_source_user(source: DiscordInteraction):
    """Get user from either interaction or context."""
    import discord

    return source.user if isinstance(source, discord.Interaction) else source.author


class UserManager:
    """Centralized user management utilities to eliminate duplicate user handling patterns."""

    @staticmethod
    async def upsert_user(user: 'discord.User | discord.Member', session: 'AsyncSession') -> 'User':
        """
        Upsert user with efficient database operations.
        locations.
        """
        user_id = str(user.id)
        current_name = user.name
        current_avatar = user.display_avatar.url

        # Use PostgreSQL UPSERT instead of SQLAlchemy for better performance
        stmt = pg_insert(User).values(
            id=user_id,
            name=current_name,
            image=current_avatar,
            lastMessageAt=datetime.now(),
            updatedAt=datetime.now(),
        )

        # On conflict: Always update timestamps, conditionally update name/image
        stmt = stmt.on_conflict_do_update(
            index_elements=['id'],
            set_={
                'name': case((User.name != stmt.excluded.name, stmt.excluded.name), else_=User.name),
                'image': case(
                    (User.image != stmt.excluded.image, stmt.excluded.image),
                    else_=User.image,
                ),
                'lastMessageAt': stmt.excluded.lastMessageAt,
                'updatedAt': stmt.excluded.updatedAt,
            },
        ).returning(User)

        return (await session.execute(stmt)).scalar_one()

    @staticmethod
    async def create_user_if_not_exists(
        user: 'discord.User | discord.Member', session: 'AsyncSession', locale: str = 'en'
    ) -> 'User':
        """
        Create user if they don't exist, otherwise return existing user.

        """
        user_id = str(user.id)

        # Check if user exists
        stmt = select(User).where(User.id == user_id)
        existing_user = (await session.execute(stmt)).scalar_one_or_none()

        if existing_user:
            return existing_user

        # Create new user with default values
        new_user = User(
            id=user_id,
            name=user.name,
            image=user.display_avatar.url,
            locale=locale,
            badges=[],
            preferredLanguages=[],
            lastMessageAt=datetime.now(),
            inboxLastReadDate=datetime.now(),
            createdAt=datetime.now(),
            updatedAt=datetime.now(),
        )
        session.add(new_user)
        await session.commit()
        return new_user

    @staticmethod
    async def update_user_activity(user_id: str, session: 'AsyncSession') -> bool:
        """
        Update user's last message timestamp and increment message count.

        """
        try:
            # Get existing user
            existing_user = await session.get(User, user_id)
            if existing_user:
                existing_user.messageCount = (existing_user.messageCount or 0) + 1
                existing_user.lastMessageAt = func.now()
                await session.commit()
                return True
            return False
        except Exception:
            return False

    @staticmethod
    async def ensure_user_exists_for_context(ctx: commands.Context) -> bool:
        """Ensure user exists for command context Returns True if user exists or was created successfully."""
        async with ctx.bot.db.get_session() as session:
            await UserManager.create_user_if_not_exists(ctx.author, session)
            return True


class UserQueries:
    """Specialized user query utilities that work with the UserManager."""

    @staticmethod
    async def get_user_with_profile_data(user: 'discord.User | discord.Member') -> Optional['User']:
        """Get user with full profile data"""
        from utils.modules.common.database import DatabaseUtils

        return await DatabaseUtils.get_user(str(user.id))

    @staticmethod
    async def get_user_locale_with_fallback(user: 'discord.User | discord.Member') -> str:
        """Get user locale with fallback to 'en'"""
        from utils.modules.common.database import DatabaseUtils

        return await DatabaseUtils.get_user_locale(str(user.id))


# Convenience functions for backward compatibility and ease of use
async def upsert_user(user: 'discord.User | discord.Member', session: 'AsyncSession') -> 'User':
    return await UserManager.upsert_user(user, session)


async def load_profile_data(user: 'discord.User | discord.Member') -> Optional['User']:
    return await UserQueries.get_user_with_profile_data(user)


async def load_user_locale(source: DiscordInteraction) -> str:
    user = get_source_user(source)
    return await UserQueries.get_user_locale_with_fallback(user)


def check_user():
    async def predicate(ctx: commands.Context) -> bool:
        return await UserManager.ensure_user_exists_for_context(ctx)

    return commands.check(predicate)
