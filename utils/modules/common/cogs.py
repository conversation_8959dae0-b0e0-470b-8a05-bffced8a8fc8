from typing import TYPE_CHECKING, Any, Awaitable, Callable
import discord
from utils.modules.common.user_utils import DiscordInteraction, load_user_locale
from discord.ext import commands

if TYPE_CHECKING:
    from main import Bot


class CogBase(commands.Cog):
    def __init__(self, bot: 'Bot'):
        self.bot = bot
        self.constants = bot.constants
        self._init_context_menus()

    async def get_locale(self, ctx_or_interaction: DiscordInteraction) -> str:
        """Get user locale."""
        return await load_user_locale(ctx_or_interaction)

    def _init_context_menus(self):
        self._context_menus: list[discord.app_commands.ContextMenu] = []

        for attr_name in dir(self):
            maybe_func = getattr(self, attr_name)
            if callable(maybe_func) and hasattr(maybe_func, '__context_menu__'):
                name, type_ = maybe_func.__context_menu__  # pyright: ignore[reportFunctionMemberAccess]
                menu = discord.app_commands.ContextMenu(name=name, callback=maybe_func, type=type_)  # pyright: ignore[reportArgumentType]
                self.bot.tree.add_command(menu)
                self._context_menus.append(menu)

    async def cog_unload(self):
        for menu in getattr(self, '_context_menus', []):
            self.bot.tree.remove_command(menu.name, type=menu.type)


def context_menu(
    name: str,
    type_: discord.AppCommandType = discord.AppCommandType.message,
):
    def decorator(func: Callable[..., Awaitable[Any]]):
        func.__context_menu__ = (name, type_)  # pyright: ignore[reportFunctionMemberAccess]
        return func

    return decorator
