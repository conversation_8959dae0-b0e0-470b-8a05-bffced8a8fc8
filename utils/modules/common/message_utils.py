import re
from typing import TYPE_CHECKING, List, Optional, Tuple
from typing_extensions import deprecated
from urllib.parse import urlparse
from unidecode import unidecode
from utils.modules.core.db.models import Hub
from utils.constants import logger

import discord

if TYPE_CHECKING:
    from main import Bot
    from utils.modules.core.db.models import Message, User, Hub
    from utils.modules.services.userService import UserService


class MessageContentProcessor:
    # Precompiled regex patterns for performance
    URL_PATTERN = re.compile(r'https?://[^\s]+')
    IMAGE_URL_PATTERN = re.compile(r'\[⁥\]\(([^)]+)\)')

    # Media type constants
    IMAGE_EXTENSIONS = frozenset(['.png', '.webp', '.jpg', '.jpeg'])
    GIF_EXTENSION = '.gif'
    TENOR_DOMAIN = 'tenor.com'

    @staticmethod
    def process_message_content(
        content: str,
        attachments: List[discord.Attachment],
        stickers: List[discord.StickerItem],
    ) -> <PERSON><PERSON>[str, List[str]]:
        """
        Process message content and add attachment/sticker URLs.

        Returns:
            Tuple of (processed_content, attachment_urls)
        """
        if not content:
            content = ''

        processed_content = content
        urls = []

        # Process attachments
        for attachment in attachments:
            if MessageContentProcessor._is_image_url(attachment.url) or MessageContentProcessor._is_gif_url(
                attachment.url
            ):
                urls.append(attachment.url)
                # Add image/GIF with special format
                processed_content += f'\n[⁥]({attachment.url})'

        # Process stickers
        for sticker in stickers:
            processed_content += f'\n[⁥]({sticker.url})'

        # Check for image/GIF URLs in content
        for url in MessageContentProcessor.URL_PATTERN.findall(processed_content):
            try:
                if MessageContentProcessor._is_gif_url(url) and not MessageContentProcessor._is_tenor_gif(url):
                    # Remove non-Tenor GIF URLs from content
                    processed_content = processed_content.replace(url, '[GIF blocked - only Tenor GIFs allowed]')
                urls.append(url)
            except Exception:
                # Skip malformed URLs
                continue

        # Add placeholder for media-only messages
        if not content.strip() and (attachments or stickers):
            if not processed_content.strip():
                processed_content = '*Message contains media that was not sent.*'

        return processed_content, urls

    @staticmethod
    @deprecated('Deprecated in broadcast v2, use MediaProcessor.process_media instead')
    def extract_image_url_from_content(content: str) -> Optional[str]:
        if '[⁥](' in content:
            match = MessageContentProcessor.IMAGE_URL_PATTERN.search(content)
            if match:
                return match.group(1)
        return None

    @staticmethod
    @deprecated('Deprecated in broadcast v2, use MediaProcessor.is_image_url instead')
    def _is_image_url(url: str) -> bool:
        """Check if URL is an image (png, webp, jpg, jpeg)."""
        try:
            parsed_url = urlparse(url.lower())
            return any(parsed_url.path.endswith(ext) for ext in MessageContentProcessor.IMAGE_EXTENSIONS)
        except (ValueError, AttributeError):
            return False

    @staticmethod
    @deprecated('Deprecated in broadcast v2, use MediaProcessor.is_gif_url instead')
    def _is_gif_url(url: str) -> bool:
        try:
            parsed_url = urlparse(url.lower())
            return parsed_url.path.endswith(MessageContentProcessor.GIF_EXTENSION)
        except (ValueError, AttributeError):
            return False

    @staticmethod
    @deprecated('Deprecated in broadcast v2, use MediaProcessor.is_tenor_gif instead')
    def _is_tenor_gif(url: str) -> bool:
        """Check if URL is from Tenor."""
        return MessageContentProcessor.TENOR_DOMAIN in url.lower()


class MessageEmbedBuilder:
    @staticmethod
    def create_message_embed(
        message: 'Message',
        author: 'User',
        max_content_length: int = 100,
        max_username_length: int = 30,
    ) -> discord.Embed:
        """
        Create embed from message and author data.
        """
        try:
            # Safely handle None values and truncate username
            username = (author.name or 'Unknown User')[:max_username_length]

            # Safely handle None content and truncate
            content = '*Message contains media*' if message.imageUrl else message.content
            if content and len(content) > max_content_length:
                content = content[:max_content_length] + '...'

            embed = discord.Embed(description=content or '*No content*')

            author_image = getattr(author, 'image', None)
            if author_image:
                embed.set_author(name=username, icon_url=author_image)

            return embed
        except Exception:
            return discord.Embed(description='Error loading message preview', color=discord.Color.red())

    @staticmethod
    @deprecated('Deprecated in broadcast v2, use MessageService.create_reply_embed instead')
    def create_reply_embed(
        message_reference: Optional[discord.MessageReference],
        reply_data: Optional[Tuple['Message', 'User', str]],
        target_channel_id: str,
        target_server_id: str,
        broadcast_message_id: Optional[str] = None,
    ) -> Optional[discord.Embed]:
        """
        Create reply embed if message is a reply.
        """
        if not message_reference or not message_reference.message_id:
            return None

        if reply_data:
            original_message, author, _ = reply_data

            embed = MessageEmbedBuilder.create_message_embed(original_message, author)

            if broadcast_message_id:
                jump_link = MessageEmbedBuilder._build_discord_jump_link(
                    target_server_id,
                    target_channel_id,
                    broadcast_message_id,
                )
                prefix = f'[**Reply To**]({jump_link})'
                embed.description = f'{prefix}: {embed.description or ""}'

            return embed

        return None

    @staticmethod
    @deprecated('Deprecated in broadcast v2, use build_discord_jump_link from utils.utils instead')
    def _build_discord_jump_link(guild_id: str, channel_id: str, message_id: str) -> str:
        """Build Discord jump link for message."""
        return f'https://discord.com/channels/{guild_id}/{channel_id}/{message_id}'


class MessageFormatter:
    @staticmethod
    @deprecated('Deprecated in broadcast v2, use FormattingService.format_webhook_name instead')
    def format_webhook_username(author_name: str, guild_name: str) -> str:
        """
        Format username for webhook display.
        """
        from utils.modules.common.validation_utils import TextValidators

        return TextValidators.sanitize_webhook_username(unidecode(f'{author_name} | {guild_name}'))

    @staticmethod
    @deprecated('Deprecated in broadcast v2, use FormattingService.format_user_badges instead')
    async def format_user_badges(bot: 'Bot', user_id: int, user_service: 'UserService', hub: Hub) -> str:
        """
        Format user badges for display.
        """
        try:
            user_data = await user_service.fetch_badges(bot, str(user_id), True, False, hub)
            show_badges, user_badges = user_data

            if not show_badges:
                return ''

            badge_emojis = [badge['icon'] for badge in user_badges]

            if badge_emojis:
                return f'{" ".join(badge_emojis)}'

            return ''
        except Exception as e:
            logger.error(f'Error in format_user_badges {e}')
            return ''

    @staticmethod
    @deprecated('Deprecated in broadcast v2, idk where though')
    def format_broadcast_content(processed_content: str, badge_prefix: str = '', reply_mention: str = '') -> str:
        """
        Format content for broadcasting.
        """
        badge_pre = f'-# {badge_prefix} ' if badge_prefix else ''
        return f'{badge_pre}\n{reply_mention}{processed_content}'


class MessageValidator:
    @staticmethod
    @deprecated('Deprecated in broadcast v2, call MessageService.validate_message_length instead')
    def validate_message_length(content: str, max_length: int = 2000) -> bool:
        return len(content or '') <= max_length

    @staticmethod
    @deprecated('Deprecated in broadcast v2, call MessageService instead')
    def is_processable_message(message: discord.Message) -> bool:
        return not (message.author.bot or message.author.system or not message.guild)


class MessageStorageHelper:
    @staticmethod
    @deprecated('Deprecated in broadcast v2, use MessageService.create_message_object instead')
    def create_message_object(
        message: discord.Message,
        hub: 'Hub',
        processed_content: str,
        referred_message: Optional['Message'] = None,
    ) -> 'Message':
        """
        Create Message object for database storage.
        """
        from utils.modules.core.db.models import Message

        # Extract image URL from processed content if present
        image_url = MessageContentProcessor.extract_image_url_from_content(processed_content)
        guild_id = str(message.guild.id) if message.guild else ''
        referred_message_id = referred_message.id if referred_message else None

        return Message(
            id=str(message.id),
            hubId=hub.id,
            content=processed_content,
            imageUrl=image_url,
            channelId=str(message.channel.id),
            guildId=guild_id,
            authorId=str(message.author.id),
            createdAt=message.created_at.replace(tzinfo=None),
            referredMessageId=referred_message_id,
        )
