from typing import List, Optional
from datetime import datetime

from sqlalchemy import select

from utils.modules.core.db.models import Hub, Connection, HubModerator
from utils.modules.hub.constants import HubPermissionLevel
from utils.modules.services.BaseService import BaseService
from utils.modules.services.permission_service import PermissionService
from utils.modules.common.database import DatabaseQueries
from utils.modules.common.validation_utils import TextValidators


class HubService(BaseService):
    async def get_hub_by_id(self, hub_id: str) -> Optional[Hub]:
        """Get a hub by its ID."""
        return await DatabaseQueries.fetch_hub_by_id(self.session, hub_id)

    async def get_hub_by_name(self, hub_name: str, include_private: bool = False) -> Optional[Hub]:
        """Get a hub by its name."""
        return await DatabaseQueries.fetch_hub_by_name(self.session, hub_name, include_private=include_private)

    async def get_public_hubs(self) -> List[Hub]:
        """Get all public hubs."""
        result = await DatabaseQueries.fetch_public_hubs(self.session)
        return list(result)

    async def get_user_hubs(self, user_id: str) -> List[Hub]:
        """Get all hubs where the user has permissions."""
        stmt = select(Hub).join(HubModerator, Hub.id == HubModerator.hubId).where(HubModerator.userId == user_id)
        result = await self.session.execute(stmt)
        return list(result.scalars().all())

    async def get_hub_connections(self, hub_id: str, connected_only: bool = True) -> List[Connection]:
        """Get all connections for a hub."""
        result = await DatabaseQueries.fetch_hub_connections(self.session, hub_id, connected_only=connected_only)
        return list(result)

    async def check_user_permission(self, hub_id: str, user_id: str) -> HubPermissionLevel:
        """Check user's permission level in a hub."""
        hub = await self.get_hub_by_id(hub_id)
        if not hub:
            return HubPermissionLevel.NONE
        perm_service = PermissionService(self.session)
        return await perm_service.get_user_permission_from_hub(hub, user_id)

    async def validate_new_hub(self, name: str, short_desc: str) -> Optional[str]:
        """
        Validates the name and description for a new hub, including a uniqueness check.
        Returns an error message string if invalid, otherwise None.
        """
        name_validation = TextValidators.validate_hub_name(name)
        if not name_validation.is_valid:
            return 'The provided hub name is invalid or contains disallowed characters.'

        desc_validation = TextValidators.validate_length(short_desc, 10, 100, 'short description')
        if not desc_validation.is_valid:
            return 'The short description must be between 10 and 100 characters.'

        escaped_name = name.replace('\\', '\\\\').replace('%', '\\%').replace('_', '\\_')
        query = select(Hub).where(Hub.name.ilike(escaped_name, escape='\\'))

        existing = (await self.session.execute(query)).scalar()
        if existing:
            return 'That hub name is already taken (names are case-insensitive). Please choose another.'

        return None

    async def create_hub(self, name: str, short_description: str, owner_id: str, is_private: bool) -> Hub:
        """
        Creates a new Hub object and persists it to the database.
        Raises IntegrityError on unique constraint violation.
        Returns the newly created Hub object.
        """
        hub = Hub(
            name=name,
            shortDescription=short_description,
            description='None',
            ownerId=owner_id,
            iconUrl='',
            rules=[],
            private=is_private,
            settings=0,
            appealCooldownHours=168,
            weeklyMessageCount=0,
            locked=False,
            nsfw=False,
            verified=False,
            partnered=False,
            featured=False,
            createdAt=datetime.now(),
            updatedAt=datetime.now(),
            lastActive=datetime.now(),
        )
        self.session.add(hub)
        await self.session.commit()
        await self.session.refresh(hub)
        return hub

    async def is_hub_locked(self, hub_id: str) -> bool:
        """Check if a hub is locked."""
        hub = await self.get_hub_by_id(hub_id)
        return hub.locked if hub else True

    async def lock_hub(self, hub_id: str, user_id: str) -> bool:
        """Lock a hub (requires appropriate permissions)."""
        permission_level = await self.check_user_permission(hub_id, user_id)
        if permission_level < HubPermissionLevel.MANAGER:
            return False

        hub = await self.get_hub_by_id(hub_id)
        if not hub:
            return False

        hub.locked = True
        await self.session.commit()
        return True

    async def unlock_hub(self, hub_id: str, user_id: str) -> bool:
        """Unlock a hub (requires appropriate permissions)."""
        permission_level = await self.check_user_permission(hub_id, user_id)
        if permission_level < HubPermissionLevel.MANAGER:
            return False

        hub = await self.get_hub_by_id(hub_id)
        if not hub:
            return False

        hub.locked = False
        await self.session.commit()
        return True

    async def update_hub_activity(self, hub_id: str) -> bool:
        """Update hub's last activity timestamp."""
        hub = await self.get_hub_by_id(hub_id)
        if not hub:
            return False

        hub.lastActive = datetime.now()
        await self.session.commit()
        return True
