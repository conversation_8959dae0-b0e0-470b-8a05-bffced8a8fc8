import json
from typing import TYPE_CHECKING, List, Optional, <PERSON><PERSON>

import discord
from sqlalchemy import select

from data.badges import badges
from utils.modules.common.database import DatabaseQueries
from utils.modules.common.service_utils import DatabaseOperationHelper
from utils.modules.core.cache import user_badge_cache
from utils.modules.core.checks import is_interchat_staff_direct
from utils.modules.core.db.models import Badges, Hub, HubModerator, HubModeratorRole, User
from utils.modules.services.BaseService import BaseService
from utils.utils import parse_discord_emoji

if TYPE_CHECKING:
    from main import Bot
    from utils.interfaces import InterChatBadge
    from utils.modules.core.db.models import Badges


class UserService(BaseService):
    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        return await DatabaseQueries.fetch_user_by_id(self.session, user_id)

    async def create_or_update(self, user_id: str, **kwargs) -> User:
        """
        DEPRECATED: This method commits prematurely. Use `upsert_user` for broadcast-related
        operations and ensure the calling function handles the commit.
        """
        user, created = await DatabaseOperationHelper.get_or_create(self.session, User, defaults=kwargs, id=user_id)

        if not created:
            for key, value in kwargs.items():
                if hasattr(user, key):
                    setattr(user, key, value)

        await self.session.commit()
        return user

    async def upsert_user(self, user: discord.User | discord.Member, commit: bool = True) -> User:
        """
        Creates a user if they don't exist, or updates their profile info if they do.
        This function is safe to use within larger transactions as it does not commit.
        """
        user_id_str = str(user.id)
        db_user = await self.session.get(User, user_id_str)
        avatar_url = user.display_avatar.url

        if db_user:
            if db_user.name != user.name or db_user.image != avatar_url:
                db_user.name = user.name
                db_user.image = avatar_url
                self.session.add(db_user)
        else:
            db_user = User(
                id=user_id_str,
                name=user.name,
                image=avatar_url,
                badges=[],
                preferredLanguages=[],
                showBadges=True,
                mentionOnReply=True,
                showNsfwHubs=False,
                voteCount=0,
                reputation=0,
                messageCount=0,
                hubJoinCount=0,
                hubEngagementScore=0.0,
                locale='en',
            )
            self.session.add(db_user)
        if commit:
            await self.session.commit()

        return db_user

    async def fetch_badges(
        self,
        bot: 'Bot',
        user_id: str,
        use_cache: bool = True,
        html_format: bool = False,
        hub: Optional[Hub] = None,
    ) -> Tuple[bool, List['InterChatBadge']]:
        if use_cache:
            cached_data = await user_badge_cache.get_user_badges(user_id)
            if cached_data:
                return cached_data

        badges_list: list['InterChatBadge'] = []
        int_user_id = int(user_id)

        if int_user_id in bot.constants.auth_users:
            badges_list.append(
                {
                    'icon': parse_discord_emoji(bot.emotes.developer_badge)
                    if html_format
                    else str(bot.emotes.developer_badge),
                    'title': 'InterChat Developer',
                    'description': 'Core developer of InterChat',
                }
            )

        if is_interchat_staff_direct(bot, int_user_id):
            badges_list.append(
                {
                    'icon': parse_discord_emoji(bot.emotes.staff_badge) if html_format else str(bot.emotes.staff_badge),
                    'title': 'InterChat Staff',
                    'description': 'InterChat staff member',
                }
            )

        res = await DatabaseQueries.fetch_user_badges(self.session, user_id)
        show_badges = True
        if res:
            db_badges, show_badges = res
            for badge in db_badges:
                badge_info = badges.get(badge)
                if not badge_info:
                    continue
                icon_attr = getattr(bot.emotes, badge_info['icon'], None)
                if not icon_attr:
                    continue
                badges_list.append(
                    {
                        'icon': parse_discord_emoji(icon_attr) if html_format else str(icon_attr),
                        'title': badge_info['name'],
                        'description': badge_info['description'],
                    }
                )

        if not html_format and hub:
            hub_id = hub.id
            custom_badges_config = {}
            if hub.customBadges:
                try:
                    if isinstance(hub.customBadges, str):
                        custom_badges_config = json.loads(hub.customBadges)
                    elif isinstance(hub.customBadges, dict):
                        custom_badges_config = hub.customBadges
                except (json.JSONDecodeError, TypeError):
                    custom_badges_config = {'owner': True, 'manager': True, 'moderator': True}

            hub_query = select(Hub.ownerId).where(Hub.id == hub_id)
            mod_query = select(HubModerator.role).where(
                HubModerator.userId == user_id,
                HubModerator.hubId == hub_id,
            )

            hub_result = await self.session.execute(hub_query)
            owner_id = hub_result.scalar_one_or_none()

            if owner_id == int_user_id:  # Cast user_id to int for comparison
                if custom_badges_config.get('owner', True):
                    badges_list.append(
                        {
                            'icon': str(bot.emotes.owner_badge),
                            'title': 'Hub Owner',
                            'description': f'Owner of the hub "{hub.name}"',
                        }
                    )
            else:
                mod_result = await self.session.execute(mod_query)
                moderator_role = mod_result.scalar_one_or_none()

                if moderator_role == HubModeratorRole.MODERATOR:
                    if custom_badges_config.get('moderator', True):
                        badges_list.append(
                            {
                                'icon': str(bot.emotes.moderator_badge),
                                'title': 'Hub Moderator',
                                'description': f'Moderator of the hub "{hub.name}"',
                            }
                        )
                elif moderator_role == HubModeratorRole.MANAGER:
                    if custom_badges_config.get('manager', True):
                        badges_list.append(
                            {
                                'icon': str(bot.emotes.manager_badge),
                                'title': 'Hub Manager',
                                'description': f'Manager of the hub "{hub.name}"',
                            }
                        )

        if use_cache:
            await user_badge_cache.set_user_badges(user_id, show_badges, badges_list)
        return show_badges, badges_list

    async def add_badge(self, user_id: str, badge: Badges) -> bool:
        user = await self.get_user_by_id(user_id)
        if not user:
            return False
        if not user.badges:
            user.badges = []
        if badge not in user.badges:
            user.badges.append(badge)
            await self.session.commit()
            await user_badge_cache.clear_user_badges(user_id)
            return True
        return False

    async def remove_badge(self, user_id: str, badge: Badges) -> bool:
        user = await self.get_user_by_id(user_id)
        if not user or not user.badges:
            return False
        if badge in user.badges:
            user.badges.remove(badge)
            await self.session.commit()
            await user_badge_cache.clear_user_badges(user_id)
            return True
        return False

    async def set_badge_visibility(self, user_id: str, show_badges: bool) -> bool:
        user = await self.get_user_by_id(user_id)
        if not user:
            return False
        user.showBadges = show_badges
        await self.session.commit()
        await user_badge_cache.clear_user_badges(user_id)
        return True

    async def increment_message_count(self, user_id: str) -> bool:
        user = await self.get_user_by_id(user_id)
        if not user:
            return False
        user.messageCount = (user.messageCount or 0) + 1
        await self.session.commit()
        return True

    async def update_reputation(self, user_id: str, change: int) -> bool:
        user = await self.get_user_by_id(user_id)
        if not user:
            return False
        user.reputation = (user.reputation or 0) + change
        await self.session.commit()
        return True
