import asyncio
from enum import Enum, auto
from typing import TYPE_CHECKING, Optional, Sequence, Tuple, Union

import discord
from discord.ext import commands
from sqlalchemy import delete, select
from sqlalchemy.ext.asyncio import AsyncSession

from utils.constants import logger
from utils.modules.common.error_utils import send_error_message as send_generic_error_message
from utils.modules.common.service_utils import retry_with_backoff
from utils.modules.common.user_utils import load_user_locale
from utils.modules.core.cache import webhook_cache
from utils.modules.core.db.models import Broadcast, Connection, Hub, Message
from utils.modules.core.i18n import t
from utils.modules.core.moderation import fetch_original_msg_with_extra
from utils.modules.errors.customErrors import MessageDeletionError
from utils.modules.events.hubLoggingHelpers import HubLogger
from utils.modules.hub.constants import HubPermissionLevel
from utils.modules.services.permission_service import PermissionService

if TYPE_CHECKING:
    from main import Bot

DiscordContext = Union[discord.Interaction['Bot'], commands.Context['Bot']]


class MessageDeleteResult(Enum):
    SUCCESS = auto()  # The message and all broadcasts were deleted.
    PARTIAL = auto()  # Broadcasts deleted, but original kept for reference (e.g., a reply).
    NOT_FOUND = auto()  # The message was not found in the database.
    FAILURE = auto()  # A general failure occurred.


class MessageDeletionService:
    def __init__(self, bot: 'Bot'):
        self.bot = bot

    # region High-Level Methods

    async def delete_message_from_context(
        self,
        ctx: DiscordContext,
        message_id: str,
        reason: Optional[str] = None,
        locale: Optional[str] = None,
    ) -> None:
        """Handles a moderator-initiated deletion command."""
        moderator = ctx.user if isinstance(ctx, discord.Interaction) else ctx.author
        locale = locale or await load_user_locale(ctx)

        try:
            async with self.bot.db.get_session() as session:
                # Step 1: Validation and Permission Check
                original_message_info = await fetch_original_msg_with_extra(session, message_id)
                if not original_message_info:
                    raise MessageDeletionError(
                        t('responses.moderation.delete.notInterChatMessage', locale=locale)
                    )

                message_obj, _, hub_id = original_message_info
                hub = await session.get(Hub, hub_id)
                if not hub:
                    raise MessageDeletionError(
                        t('responses.moderation.errors.hubNotFoundForMessage', locale=locale)
                    )

                await self._validate_moderator_permissions(
                    session, moderator, hub, message_obj.authorId, locale
                )

            result = await self.execute_deletion(
                message_id=message_id,
                reason=reason,
                moderator_id=str(moderator.id),
                moderator_name=str(moderator),
            )

            if result == MessageDeleteResult.NOT_FOUND:
                raise MessageDeletionError(
                    t('responses.moderation.delete.notInterChatMessage', locale=locale)
                )

            await self._send_success_message(ctx, locale)

        except MessageDeletionError as e:
            await self._send_error_message(ctx, str(e), locale)
        except Exception as e:
            logger.exception(f'An unexpected error occurred in delete_message_from_context: {e}')
            await self._send_error_message(ctx, t('responses.errors.whoops', locale=locale), locale)

    # endregion

    # region Core Deletion Logic

    async def execute_deletion(
        self,
        message_id: str,
        reason: Optional[str] = None,
        moderator_id: Optional[str] = None,
        moderator_name: Optional[str] = None,
    ) -> MessageDeleteResult:
        """
        Executes the low-level deletion of a message and its broadcasts.
        """
        logger.info(f'Executing deletion for message ID: {message_id}')
        async with self.bot.db.get_session() as session:
            # 1. Fetch all necessary data in one go
            message_record = await session.get(Message, message_id)
            if not message_record:
                logger.warning(f'Message {message_id} not found in DB for deletion. Aborting.')
                return MessageDeleteResult.NOT_FOUND

            broadcast_records = await session.execute(
                select(Broadcast.channelId, Broadcast.id, Connection.parentId)
                .join(Connection, Connection.channelId == Broadcast.channelId)
                .where(Broadcast.messageId == message_id)
            )
            broadcasts_to_delete = broadcast_records.tuples().all()

            await self._delete_broadcasted_messages(broadcasts_to_delete)

            await session.execute(delete(Broadcast).where(Broadcast.messageId == message_id))

            # TODO: Check for reports and other relationshits as well
            has_references = await session.scalar(
                select(select(Message.id).where(Message.referredMessageId == message_id).exists())
            )

            if not has_references:
                await session.delete(message_record)
                result = MessageDeleteResult.SUCCESS
            else:
                logger.info(f'Message {message_id} has references; keeping DB record.')
                result = MessageDeleteResult.PARTIAL

            await session.commit()
            logger.info(f'Successfully cleaned DB records for message {message_id}.')

            # Log the moderation action after the operation is complete.
            if moderator_id and moderator_name:
                await self._log_deletion_action(
                    session=session,
                    message_record=message_record,
                    moderator_id=moderator_id,
                    moderator_name=moderator_name,
                    reason=reason,
                )

            return result

    async def _delete_broadcasted_messages(
        self, broadcasts: Sequence[Tuple[str, str, Optional[str]]]
    ) -> None:
        if not broadcasts:
            return

        logger.info(f'Attempting to delete {len(broadcasts)} broadcasted messages from Discord.')

        tasks = [
            self._delete_single_webhook_message(channel_id, msg_id, bool(parent_id))
            for channel_id, msg_id, parent_id in broadcasts
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        failed_count = sum(1 for r in results if isinstance(r, Exception))
        if failed_count > 0:
            logger.warning(
                f'Failed to delete {failed_count} of {len(broadcasts)} messages after retries.'
            )

    @retry_with_backoff(retries=3, base_delay=1)
    async def _delete_single_webhook_message(
        self, channel_id: str, message_id: str, is_thread: bool
    ) -> None:
        """Deletes a single message using its channel's webhook."""
        try:
            webhook = await self._get_webhook_for_channel(channel_id)
            if not webhook:
                raise MessageDeletionError(f'No webhook found for channel {channel_id}')

            if is_thread:
                target_thread = discord.Object(id=int(channel_id))
                await webhook.delete_message(int(message_id), thread=target_thread)
            else:
                await webhook.delete_message(int(message_id))

        except discord.NotFound:
            logger.debug(f'Message {message_id} in channel {channel_id} was already deleted.')
        except discord.Forbidden as e:
            logger.error(
                f'Webhook lost permissions to delete message? This is bad, really bad message: {message_id} in {channel_id}: {e}'
            )
            await webhook_cache.clear_webhook_url(channel_id)
            raise  # Re-raise to let the retry handler attempt it again
        except Exception as e:
            logger.exception(
                f'Unexpected error deleting {message_id} in {channel_id}: {e}', exc_info=True
            )
            raise

    # endregion

    # region Helper & Utility Methods

    async def _log_deletion_action(
        self,
        session: 'AsyncSession',
        message_record: 'Message',
        moderator_id: str,
        moderator_name: str,
        reason: Optional[str],
    ) -> None:
        try:
            hub = await session.get(Hub, message_record.hubId)
            await HubLogger.log_message_delete(
                hub_id=message_record.hubId,
                hub_name=hub.name if hub else 'Unknown Hub',
                message_id=str(message_record.id),
                channel_id=message_record.channelId,
                original_content=message_record.content,
                moderator_id=moderator_id,
                moderator_name=moderator_name,
                reason=reason,
            )
            logger.debug(f'Logged deletion for message {message_record.id}.')
        except Exception as e:
            logger.error(f'Failed to log message deletion for {message_record.id}: {e}')

    async def _validate_moderator_permissions(
        self,
        session: AsyncSession,
        moderator: Union[discord.User, discord.Member],
        hub: Hub,
        message_author_id: str,
        locale: str,
    ) -> None:
        """Checks if a user has permission to delete a message."""
        if moderator.id == int(message_author_id):
            return  # Users can always delete their own messages.

        perm_service = PermissionService(session)
        has_perm, _ = await perm_service.check_permission_from_hub(
            hub, str(moderator.id), HubPermissionLevel.MODERATOR
        )
        if not has_perm:
            perm_name = HubPermissionLevel.MODERATOR.name.title()
            raise MessageDeletionError(
                t(
                    'responses.infractions.permissions.insufficient',
                    locale=locale,
                    permission=perm_name,
                )
            )

    async def _get_webhook_for_channel(self, channel_id: str) -> Optional[discord.Webhook]:
        """Retrieves a webhook for a given channel, using the cache first."""
        cached_url = await webhook_cache.get_webhook_url(channel_id)
        if cached_url:
            return discord.Webhook.from_url(cached_url, session=self.bot.http_session)

        async with self.bot.db.get_session() as session:
            db_url = await session.scalar(
                select(Connection.webhookURL).where(Connection.channelId == channel_id)
            )

        if db_url:
            await webhook_cache.set_webhook_url(channel_id, db_url)
            return discord.Webhook.from_url(db_url, session=self.bot.http_session)

        return None

    async def _send_success_message(self, ctx: DiscordContext, locale: str) -> None:
        """Sends a standardized success embed."""
        embed = discord.Embed(
            description=f'{self.bot.emotes.tick} {t("responses.moderation.delete.success", locale=locale)}',
            color=discord.Color.green(),
        )
        await self._send_response(ctx, embed=embed)

    async def _send_error_message(self, ctx: DiscordContext, error: str, locale: str) -> None:
        """Sends a standardized error embed."""
        await send_generic_error_message(
            ctx, error, title=t('ui.common.titles.error', locale=locale), ephemeral=True
        )

    async def _send_response(self, ctx: DiscordContext, embed: discord.Embed) -> None:
        """Sends a response, handling both Interactions and classic Commands."""
        if isinstance(ctx, discord.Interaction):
            # Interactions require using followup if a response has already been sent.
            if not ctx.response.is_done():
                await ctx.response.send_message(embed=embed, ephemeral=True)
            else:
                await ctx.followup.send(embed=embed, ephemeral=True)
        else:
            await ctx.send(embed=embed)

    # endregion


async def delete_interchat_message(
    bot: 'Bot',
    message_id: str,
    reason: Optional[str] = None,
    moderator_id: Optional[str] = None,
    moderator_name: Optional[str] = None,
) -> MessageDeleteResult:
    service = MessageDeletionService(bot)
    return await service.execute_deletion(
        message_id=message_id,
        reason=reason,
        moderator_id=moderator_id,
        moderator_name=moderator_name,
    )
