from __future__ import annotations

import json
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import yaml


class LocaleManager:
    def __init__(self, locales_path: Union[str, Path] = 'locales/'):
        self.locales = self._load_locales(Path(locales_path))

    def _load_locales(self, path: Path) -> Dict[str, Any]:
        """Loads locales from a given file or directory path."""
        if not path.exists():
            raise FileNotFoundError(f'Locales path not found: {path}')

        try:
            if path.is_file():
                return self._load_single_file(path)
            if path.is_dir():
                return self._load_locales_from_dir(path)

            raise ValueError(f'The provided path is not a valid file or directory: {path}')
        except Exception as e:
            raise RuntimeError(f'Error loading locales from {path}: {e}') from e

    def _load_locales_from_dir(self, dir_path: Path) -> Dict[str, Any]:
        locales: Dict[str, Dict[str, Any]] = {}
        for lang_dir in dir_path.iterdir():
            if lang_dir.is_dir():
                language_code = lang_dir.name
                locales[language_code] = self._load_language_dir(lang_dir)

        if not locales:
            raise ValueError(f'No valid language directories found in: {dir_path}')

        return locales

    def _load_language_dir(self, lang_dir_path: Path) -> Dict[str, Any]:
        """Loads and merges all translation files for a single language."""
        aggregated: Dict[str, Any] = {}
        for file_path in lang_dir_path.glob('*.y*ml'):
            self._merge_dict(aggregated, self._load_single_file(file_path))
        for file_path in lang_dir_path.glob('*.json'):
            self._merge_dict(aggregated, self._load_single_file(file_path))
        return aggregated

    def _load_single_file(self, file_path: Path) -> Dict[str, Any]:
        """Loads a single YAML or JSON file."""
        with file_path.open('r', encoding='utf-8') as file:
            content = {}
            if file_path.suffix in ('.yaml', '.yml'):
                content = yaml.safe_load(file)
            elif file_path.suffix == '.json':
                content = json.load(file)
            return content or {}

    def _merge_dict(self, base: Dict[str, Any], other: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively merges the 'other' dictionary into the 'base' dictionary."""
        for k, v in other.items():
            if isinstance(v, dict) and isinstance(base.get(k), dict):
                base[k] = self._merge_dict(base[k], v)
            else:
                base[k] = v
        return base

    def _get_nested_text(self, language: str, key_path: str) -> Optional[Any]:
        keys = key_path.split('.')
        current_level = self.locales.get(language, {})
        for key in keys:
            if isinstance(current_level, dict) and key in current_level:
                current_level = current_level[key]
            else:
                return None
        return current_level

    def get_text(self, language: str, key_path: str, **kwargs) -> str:
        text = self._get_nested_text(language, key_path)

        # Fallback to English if not found
        if text is None and language != 'en':
            text = self._get_nested_text('en', key_path)

        # If still not found, return the key path as a last resort
        if text is None:
            return key_path

        if isinstance(text, str):
            try:
                return text.format(**kwargs)
            except (KeyError, IndexError):
                return text

        return str(text)

    def get_available_languages(self) -> List[str]:
        return sorted(self.locales.keys())

    def has_language(self, language: str) -> bool:
        return language in self.locales


locale_manager = LocaleManager()


def t(key: str, locale: str = 'en', **kwargs) -> str:
    """Translate a key using the given locale using Dot-notation paths.

    Example:
        t("commands.help.name", locale="en")
    """
    return locale_manager.get_text(locale, key, **kwargs)
