from utils.constants import redis_client, logger
from redis.exceptions import RedisError
import asyncio
from typing import Optional


async def warm_redis_pool(connections_to_warm: Optional[int] = None):
    """
    cook dem bitches up
    """
    pool = redis_client.connection_pool

    if connections_to_warm is None:
        connections_to_warm = pool.max_connections

    if not connections_to_warm:
        logger.warning("No connections to warm. `max_connections` may be unset.")
        return

    logger.info(f"Warming up {connections_to_warm} Redis connections...")

    tasks = [redis_client.ping() for _ in range(connections_to_warm)]

    results = await asyncio.gather(*tasks, return_exceptions=True)

    successful_pings = sum(1 for res in results if isinstance(res, bool) and res)

    if successful_pings == connections_to_warm:
        logger.info(f"Successfully warmed up {successful_pings}/{connections_to_warm} Redis connections. Pool is ready.")
    else:
        logger.error(f"Failed to warm up all connections. Success: {successful_pings}/{connections_to_warm}.")

async def validate_redis_connection():
    try:
        response = await redis_client.ping()
        if response:
            logger.info('Initialized Redis successfully')
        else:
            logger.critical('Redis ping returned falsy; stopping bot.')
            raise SystemExit(1)
    except RedisError as e:
        logger.critical(f'Failed to connect to Redis: {e}', exc_info=e)
        raise SystemExit(1)
