from typing import AsyncIterator
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker, AsyncSession
from contextlib import asynccontextmanager
from utils.modules.core.db.models import Base
from utils.constants import logger, constants


class Database:
    def __init__(self, database_url: str | None = None):
        self.database_url = database_url or constants.database_url
        if not self.database_url:
            raise ValueError('DATABASE_URL environment variable is required')

        try:
            self.engine = create_async_engine(
                self.database_url,
                echo=False,
                pool_size=20,
                max_overflow=10,
                pool_pre_ping=True,
                pool_recycle=300,
            )
            self.async_session = async_sessionmaker(bind=self.engine, expire_on_commit=False, class_=AsyncSession)
            logger.info('Database engine creation successful')
        except Exception as e:
            logger.error(f'Failed to create database engine: {e}')
            raise ValueError('Invalid DATABASE_URL or connection options')

    async def create_tables(self):
        """Create all database tables"""
        try:
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            logger.info('Database tables created successfully')
        except Exception as e:
            logger.error(f'Failed to create database tables: {e}')
            raise

    async def drop_tables(self):
        """Drop all database tables (backup before running this or you're cooked)"""
        try:
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.drop_all)
            logger.info('Database tables dropped successfully')
        except Exception as e:
            logger.error(f'Failed to drop database tables: {e}')
            raise

    async def dispose(self):
        await self.engine.dispose()

    @asynccontextmanager
    async def get_session(self) -> AsyncIterator[AsyncSession]:
        session: AsyncSession = self.async_session()
        try:
            yield session
            # NOTE: Does NOT do automatically commit! Services must call session.commit() explicitly
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


# Global database instance
db = None


def init_database(database_url: str | None = None):
    global db
    db = Database(database_url)
    return db


def get_db():
    if db is None:
        raise RuntimeError('Database not initialized. Call init_database() first.')
    return db
