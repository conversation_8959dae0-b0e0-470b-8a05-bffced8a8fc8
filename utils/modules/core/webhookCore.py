from __future__ import annotations
from typing import TYPE_CHECKING, Optional, Tuple, List, Union
import discord
from discord.abc import GuildChannel
from sqlalchemy import select
import asyncio

from utils.constants import logger
from utils.modules.core.i18n import t
from utils.modules.core.db.models import Connection
from utils.modules.core.rateLimit import webhook_rate_limit

if TYPE_CHECKING:
    from main import Bot

AcceptedChannels = Union[discord.TextChannel, discord.Thread, discord.ForumChannel]


async def _get_or_create_webhook(
    bot: 'Bot', channel: AcceptedChannels, name: str
) -> Optional[discord.Webhook]:
    if isinstance(channel, discord.Thread):
        target_channel = channel.parent
    else:
        target_channel = channel

    if not isinstance(target_channel, (discord.TextChannel, discord.ForumChannel)):
        logger.warning(
            f'Cannot manage webhooks for channel type {type(target_channel).__name__} (ID: {target_channel.id if target_channel else "N/A"})'
        )
        return None

    try:
        webhooks = await target_channel.webhooks()
        for webhook in webhooks:
            if bot.user and webhook.user and webhook.user.id == bot.user.id and webhook.name == 'InterChat Core':
                return webhook
    except discord.Forbidden:
        logger.error(f"Missing 'Manage Webhooks' permission in channel {target_channel.id}")
        return None
    except Exception as e:
        logger.error(f'Failed to fetch webhooks for channel {target_channel.id}: {e}')
        return None

    try:
        await webhook_rate_limit(target_channel)
        webhook = await target_channel.create_webhook(name=name, reason='InterChat Connection Setup')
        return webhook
    except discord.Forbidden:
        logger.error(f"Missing 'Manage Webhooks' permission to create webhook in channel {target_channel.id}")
    except Exception as e:
        logger.error(f'Failed to create webhook for channel {target_channel.id}: {e}')

    return None


async def validate_connection_webhooks(
    bot: 'Bot', channel: AcceptedChannels
) -> Tuple[Optional[discord.Webhook], Optional[discord.Webhook]]:

    primary_task = _get_or_create_webhook(bot, channel, name='InterChat Core 1')
    secondary_task = _get_or_create_webhook(bot, channel, name='InterChat Core 2')

    primary_webhook, secondary_webhook = await asyncio.gather(primary_task, secondary_task)

    if not primary_webhook or not secondary_webhook:
        logger.error(f'Failed to create one or both webhooks for channel {channel.id}.')

    return primary_webhook, secondary_webhook


async def validate_webhook(bot: 'Bot', channel: AcceptedChannels) -> Optional[discord.Webhook]:

    webhook = await _get_or_create_webhook(bot, channel, name='InterChat Core 1')

    if not webhook:
        logger.error(f'Webhook validation failed for channel {channel.id}: no webhook available.')
        return None

    async with bot.db.get_session() as session:
        stmt = select(Connection).where(Connection.channelId == str(channel.id)).limit(1)
        connection = await session.scalar(stmt)

        if not connection:
            logger.warning(f'No connection found for channel {channel.id} during webhook validation.')
            return webhook

        if connection.webhookURL != webhook.url:
            connection.webhookURL = webhook.url
            await session.commit()
            logger.info(f'Updated webhook URL for channel {channel.id}')
        else:
            logger.debug(f'Webhook URL is already up-to-date for channel {channel.id}')

    return webhook


async def cleanup_webhooks(bot: 'Bot', channel: GuildChannel):

    target_channel = channel.parent if isinstance(channel, discord.Thread) else channel
    if not isinstance(target_channel, (discord.TextChannel, discord.ForumChannel)):
        return

    try:
        webhooks = await target_channel.webhooks()
        deleted_count = 0
        for webhook in webhooks:
            if bot.user and webhook.user and webhook.user.id == bot.user.id:
                try:
                    await webhook.delete()
                    deleted_count += 1
                    logger.debug(f'Deleted InterChat webhook {webhook.id} from channel {target_channel.id}')
                except Exception as e:
                    logger.warning(f'Failed to delete webhook {webhook.id}: {e}')

        if deleted_count > 0:
            logger.info(f'Cleaned up {deleted_count} InterChat webhooks from channel {target_channel.id}')

    except Exception as e:
        logger.error(f'Failed to fetch webhooks for cleanup in channel {target_channel.id}: {e}')


async def fix_connections(
    bot: 'Bot', guild: discord.Guild, locale
) -> Tuple[List[Tuple[Connection, str]], List[Tuple[Connection, str]]]:
    user_errors: List[Tuple[Connection, str]] = []
    fixed_connections: List[Tuple[Connection, str]] = []

    async with bot.db.get_session() as session:
        stmt = select(Connection).where(Connection.serverId == str(guild.id))
        connections = (await session.execute(stmt)).scalars().all()

        for conn in connections:
            channel = guild.get_channel(int(conn.channelId))
            if not channel:
                await session.delete(conn)
                user_errors.append(
                    (
                        conn,
                        f'{bot.emotes.trash_icon} {t("commands.connections.fix.responses.errors.channelDeleted", locale)}',
                    )
                )
                continue

            permissions = channel.permissions_for(guild.me)
            if not permissions.view_channel or not permissions.manage_webhooks or not permissions.send_messages:
                user_errors.append(
                    (
                        conn,
                        f'{bot.emotes.globe_icon} {t("commands.connections.fix.responses.errors.permissionsSend", locale)}',
                    )
                )
                continue

            assert isinstance(channel, AcceptedChannels), f'Channel {channel.id} is not a valid text channel type.'
            primary_webhook, secondary_webhook = await validate_connection_webhooks(bot, channel)

            if primary_webhook and secondary_webhook:
                urls_changed = False
                if conn.webhookURL != primary_webhook.url:
                    conn.webhookURL = primary_webhook.url
                    urls_changed = True
                if conn.webhookSecondaryURL != secondary_webhook.url:
                    conn.webhookSecondaryURL = secondary_webhook.url
                    urls_changed = True

                if urls_changed:
                    fixed_connections.append(
                        (conn, f'{bot.emotes.tick} {t("commands.connections.fix.responses.success.fixed", locale)}')
                    )
                else:
                    fixed_connections.append(
                        (conn, f'{bot.emotes.tick} {t("commands.connections.fix.responses.success.valid", locale)}')
                    )
            else:
                user_errors.append(
                    (
                        conn,
                        f'{bot.emotes.link_icon} {t("commands.connections.fix.responses.errors.permissionsWebhook", locale)}',
                    )
                )

        await session.commit()

    return fixed_connections, user_errors
