from typing import TYPE_CHECKING

import discord
from discord.ext import commands

from utils.modules.common.cogs import CogBase
from utils.modules.common.embeds import CommonErrors
from utils.modules.core.db.models import Hub
from utils.modules.core.i18n import t
from utils.modules.core.moderation import get_user_moderated_hubs
from ..services.action_handler import ModerationActionHandler
from ..services.types import ActionType, ModerationTarget
from utils.modules.services.permission_service import PermissionService
from utils.modules.hub.constants import HubPermissionLevel
from .helpers import (
    fetch_hub,
    fetch_message_data,
    fetch_target_entities,
    parse_args_for_target_and_reason,
)

if TYPE_CHECKING:
    from main import Bot


class ModerationBaseCog(CogBase):
    def __init__(self, bot: 'Bot'):
        self.bot = bot
        self.constants = bot.constants
        self.locale = 'en'

    async def handle_message_mod_action(
        self,
        ctx: commands.Context,
        action: ActionType,
        message: discord.Message,
        args: str,
    ):
        msg_result = await fetch_message_data(self.bot, message)
        if not msg_result:
            return await ctx.send(
                f'{self.bot.emotes.x_icon} '
                + t('responses.moderation.errors.originalMessageNotFound', locale=self.locale),
                ephemeral=True,
            )

        original_message, _, _ = msg_result
        target_user, target_server = await fetch_target_entities(self.bot, original_message)

        if not target_user or not target_server:
            embed = discord.Embed(
                title=t('ui.common.titles.error', locale=self.locale),
                description=f'{self.bot.emotes.x_icon} {t("responses.moderation.errors.fetchAuthorOrServerFailed", locale=self.locale)}',
                color=discord.Color.red(),
            )
            return await ctx.send(embed=embed, ephemeral=True)

        selected_hub = await fetch_hub(self.bot, original_message.hubId)
        if not selected_hub:
            embed = discord.Embed(
                title=t('ui.common.titles.error', locale=self.locale),
                description=f'{self.bot.emotes.x_icon} {t("responses.moderation.errors.hubNotFoundForMessage", locale=self.locale)}',
                color=discord.Color.red(),
            )
            return await ctx.send(embed=embed, ephemeral=True)

        target = ModerationTarget(user=target_user, server=None)
        if 'server' in args.lower():
            target = ModerationTarget(user=None, server=target_server)
            args = args.replace('server', '').replace('Server', '').replace('SERVER', '').strip()

        await self.execute_direct_action(ctx, action, target, selected_hub, args)

    async def execute_direct_action(
        self,
        ctx: commands.Context,
        action: ActionType,
        target: ModerationTarget,
        hub: Hub,
        args: str,
    ):
        reason, duration_ms, _ = parse_args_for_target_and_reason(args, action)

        if action == ActionType.MUTE and duration_ms is None:
            embed = discord.Embed(
                title=t('ui.common.titles.error', locale=self.locale),
                description=f'{self.bot.emotes.x_icon} Duration is required for mute actions. Use format like "1d", "2h", "30m".',
                color=discord.Color.red(),
            )
            return await ctx.send(embed=embed, ephemeral=True)

        handler = ModerationActionHandler(self.bot, ctx.author, hub, self.locale)

        if action in [ActionType.WARN, ActionType.MUTE, ActionType.BAN]:
            await handler.handle_punitive_action(ctx, action, target, reason, duration_ms)
        elif action in [ActionType.UNMUTE, ActionType.UNBAN]:
            await handler.handle_revoke_action(ctx, action, target)

    async def handle_no_target_error(self, ctx: commands.Context):
        embed = discord.Embed(
            title=t('ui.common.titles.error', locale=self.locale),
            description=f'{self.bot.emotes.x_icon} {t("responses.moderation.errors.noTarget", self.locale)}',
            color=discord.Color.red(),
        )
        await ctx.send(embed=embed, ephemeral=True)

    async def check_user_has_moderated_hubs(self, ctx: commands.Context):
        user_hubs = await get_user_moderated_hubs(self.bot, str(ctx.author.id))
        if not user_hubs:
            await ctx.send(
                f'{self.bot.emotes.x_icon} ' + t('responses.moderation.errors.noModeratedHubs', locale=self.locale),
                ephemeral=True,
            )
            return None
        return user_hubs

    async def check_manager_permission(self, ctx: commands.Context, hub: Hub) -> bool:
        async with self.bot.db.get_session() as session:
            perm_service = PermissionService(session)
            has_perm, _ = await perm_service.check_permission_from_hub(
                hub, str(ctx.author.id), HubPermissionLevel.MANAGER
            )
        if not has_perm:
            embed = CommonErrors.manager_permission_required(self.bot, self.locale)
            await ctx.send(embed=embed, ephemeral=True)
            return False
        return True
