import json
from typing import TYPE_CHECKING, Optional, <PERSON><PERSON>

import discord
from discord.ext import commands

from utils.modules.common.database import DatabaseQueries
from utils.modules.core.db.models import Hub
from utils.modules.core.i18n import t
from utils.modules.core.moderation import fetch_original_message, fetch_original_msg_with_extra
from utils.modules.services.hubService import HubService
from ..services.types import ActionType
from utils.utils import parse_duration

if TYPE_CHECKING:
    from main import Bot


async def fetch_hub(bot: 'Bot', hub_id: str) -> Optional[Hub]:
    async with bot.db.get_session() as session:
        return await DatabaseQueries.fetch_hub_with_moderators(session, hub_id)


async def fetch_hub_by_name(bot: 'Bot', hub_name: str) -> Optional[Hub]:
    async with bot.db.get_session() as session:
        hub_service = HubService(session)
        return await hub_service.get_hub_by_name(hub_name, include_private=True)


def parse_hub_from_json(hub: str) -> Optional[dict[str, str]]:
    try:
        return json.loads(hub)
    except json.JSONDecodeError:
        return None


async def validate_and_get_hub(
    bot: 'Bot', ctx: commands.Context, hub_name: Optional[str], locale: str = 'en'
) -> Optional[Hub]:
    async with bot.db.get_session() as session:
        hubId: Optional[str] = None
        if not ctx.interaction and ctx.message.reference and hub_name is None:
            original_msg = await fetch_original_message(session, str(ctx.message.reference.message_id))
            hubId = original_msg.hubId if original_msg else hubId

    selected_hub = (
        await fetch_hub_by_name(bot, hub_name) if hub_name else await fetch_hub(bot, hubId) if hubId else None
    )

    if not selected_hub:
        await ctx.send(
            f'{bot.emotes.x_icon} ' + t('ui.common.messages.hubNotFound', locale=locale),
            ephemeral=True,
        )
        return None

    return selected_hub


def parse_args_for_target_and_reason(args: str, action: ActionType) -> Tuple[Optional[str], Optional[int], str]:
    if not args:
        return None, None, ''

    parts = args.split()
    duration_ms = None
    reason_parts = []
    i = 0

    if action == ActionType.MUTE and i < len(parts):
        try:
            duration_ms = parse_duration(parts[i])
            i += 1
        except ValueError:
            pass

    if i < len(parts):
        reason_parts = parts[i:]

    reason = ' '.join(reason_parts) if reason_parts else None
    return reason, duration_ms, args


async def get_message_context(ctx: commands.Context, message: Optional[discord.Message]):
    if not message and ctx.message.reference:
        ref = ctx.message.reference
        if ref and isinstance(ref.resolved, discord.Message):
            message = ref.resolved
    return message


async def fetch_message_data(bot: 'Bot', message: discord.Message):
    async with bot.db.get_session() as session:
        return await fetch_original_msg_with_extra(session, str(message.id))


async def fetch_target_entities(bot: 'Bot', original_message):
    try:
        target_user = await bot.fetch_user(int(original_message.authorId))
        target_server = await bot.fetch_guild(int(original_message.guildId))
        return target_user, target_server
    except (discord.NotFound, ValueError):
        return None, None
