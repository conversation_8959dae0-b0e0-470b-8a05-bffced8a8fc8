from typing import TYPE_CHECKING, Optional

import discord
from discord.ext import commands

from utils.modules.common.embeds import CommonErrors
from utils.modules.core.i18n import t
from ..services.moderation_service import ModerationService
from utils.modules.ui.AutoComplete import hubm_autocomplete
from ..core.helpers import validate_and_get_hub

if TYPE_CHECKING:
    from main import Bot
    from ..core.base import ModerationBaseCog


class ModerationManagementCommands:
    """Command implementations for moderation management."""

    def __init__(self, cog: 'ModerationBaseCog'):
        self.cog = cog
        self.bot = cog.bot
        self.locale = cog.locale

    @hubm_autocomplete
    async def delete_infraction(
        self,
        ctx: commands.Context['Bot'],
        hub: Optional[str],
        infraction_id: str,
    ):
        """Delete an infraction (requires Manager+ permissions)."""
        await ctx.defer()

        selected_hub = await validate_and_get_hub(self.bot, ctx, hub, self.locale)
        if not selected_hub:
            return

        if not await self.cog.check_manager_permission(ctx, selected_hub):
            return

        async with self.bot.db.get_session() as session:
            modsvc = ModerationService(session)
            success = await modsvc.delete_infraction(infraction_id)

        if not success:
            embed = CommonErrors.infraction_not_found(self.bot, self.locale)
            return await ctx.send(embed=embed, ephemeral=True)

        embed = discord.Embed(
            title=t('ui.common.titles.success', locale=self.locale),
            description=f'{self.bot.emotes.tick} {t("responses.infractions.delete.success", self.locale)}',
            color=discord.Color.green(),
        )
        await ctx.send(embed=embed, ephemeral=True)