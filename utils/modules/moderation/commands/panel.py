from typing import TYPE_CHECKING, Optional
from collections.abc import Sequence

import discord
from discord.ext import commands

from utils.modules.core.i18n import t
from utils.modules.core.moderation import mod_panel_embed
from utils.modules.core.db.models import Hub
from ..core.helpers import (
    fetch_hub,
    fetch_message_data,
    fetch_target_entities,
    get_message_context,
)
from ..ui.views.selection import HubSelectionView
from ..ui.views.panels import ModPanelView

if TYPE_CHECKING:
    from ..core.base import ModerationBaseCog


class ModerationPanelCommands:
    """Command implementations for moderation panel."""

    def __init__(self, cog: 'ModerationBaseCog'):
        self.cog = cog
        self.bot = cog.bot
        self.locale = cog.locale

    async def panel(
        self,
        ctx: commands.Context[commands.Bot],
        user: Optional[discord.User] = None,
        message: Optional[discord.Message] = None,
        server: Optional[discord.Guild] = None,
    ):
        """Show moderation panel for a user, server, or message."""
        await ctx.defer()

        message = await get_message_context(ctx, message)
        user_hubs = await self.cog.check_user_has_moderated_hubs(ctx)
        if not user_hubs:
            return

        if message:
            await self._handle_message_panel(ctx, message)
        else:
            await self._handle_target_panel(ctx, user, server, user_hubs)

    async def _handle_message_panel(self, ctx: commands.Context, message: discord.Message):
        """Handle panel for a specific message."""
        msg_result = await fetch_message_data(self.bot, message)
        if not msg_result:
            return await ctx.send(
                f'{self.bot.emotes.x_icon} '
                + t('responses.moderation.errors.originalMessageNotFound', locale=self.locale),
                ephemeral=True,
            )

        original_message, _, _ = msg_result
        target_user, target_server = await fetch_target_entities(self.bot, original_message)

        if not target_user or not target_server:
            embed = discord.Embed(
                title=t('ui.common.titles.error', locale=self.locale),
                description=f'{self.bot.emotes.x_icon} {t("responses.moderation.errors.fetchAuthorOrServerFailed", locale=self.locale)}',
                color=discord.Color.red(),
            )
            return await ctx.send(embed=embed, ephemeral=True)

        selected_hub = await fetch_hub(self.bot, original_message.hubId)
        if not selected_hub:
            embed = discord.Embed(
                title=t('ui.common.titles.error', locale=self.locale),
                description=f'{self.bot.emotes.x_icon} {t("responses.moderation.errors.hubNotFoundForMessage", locale=self.locale)}',
                color=discord.Color.red(),
            )
            return await ctx.send(embed=embed, ephemeral=True)

        view = ModPanelView(
            self.bot,
            ctx.author,
            target_user,
            target_server,
            message,
            selected_hub,
            self.locale,
        )

        embed = mod_panel_embed(
            self.bot,
            selected_hub,
            target_user,
            target_server,
            message,
            original_message.content,
            user_infractions=69,
            server_infractions=69,
            _locale=self.locale,
        )
        await ctx.send(embed=embed, view=view)

    async def _handle_target_panel(
        self,
        ctx: commands.Context,
        user: Optional[discord.User],
        server: Optional[discord.Guild],
        user_hubs: Sequence[Hub],
    ):
        """Handle panel for a user/server target."""
        if user and server:
            embed = discord.Embed(
                title=t('ui.common.titles.error', locale=self.locale),
                description=f'{self.bot.emotes.x_icon} {t("responses.moderation.target.both", locale=self.locale)}',
                color=discord.Color.red(),
            )
            return await ctx.send(embed=embed, ephemeral=True)

        view = HubSelectionView(
            self.bot,
            ctx.author,
            user,
            server,
            None,
            user_hubs,
            self.locale,
        )

        embed = discord.Embed(
            title=t('ui.moderation.hubSelection.title', locale=self.locale),
            description=t('ui.moderation.hubSelection.description', locale=self.locale),
            color=self.cog.constants.color,
        )

        if user:
            embed.add_field(
                name=t('ui.moderation.targetSelection.userField', locale=self.locale),
                value=f'{user.mention} (`{user.id}`)',
                inline=True,
            )
        if server:
            embed.add_field(
                name=t('ui.moderation.targetSelection.serverField', locale=self.locale),
                value=f'**{server.name}** (`{server.id}`)',
                inline=True,
            )

        embed.add_field(
            name=t('ui.moderation.hubSelection.fieldHubLabel', locale=self.locale),
            value=t('ui.moderation.hubSelection.fieldHubPrompt', locale=self.locale),
            inline=False,
        )

        await ctx.send(embed=embed, view=view)
