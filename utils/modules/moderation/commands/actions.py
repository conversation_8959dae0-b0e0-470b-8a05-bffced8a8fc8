from typing import TYPE_CHECKING, Optional

import discord
from discord.ext import commands

from utils.modules.core.moderation import get_user_moderated_hubs
from utils.modules.core.i18n import t
from ..core.helpers import get_message_context, validate_and_get_hub
from ..services.types import ActionType, ModerationTarget
from ..ui.views.selection import ActionHubSelectionView

if TYPE_CHECKING:
    from main import Bot
    from ..core.base import ModerationBaseCog


class ModerationActionsCommands:
    def __init__(self, cog: 'ModerationBaseCog'):
        self.cog = cog
        self.bot = cog.bot
        self.locale = cog.locale

    async def ban(
        self,
        ctx: commands.Context['Bot'],
        *,
        reason: str = '',
        user: Optional[discord.User] = None,
        message: discord.Message = None,  # type: ignore
        server: discord.Guild = None,  # type: ignore
        hub: Optional[str] = None,
    ):
        """Ban a user or server from a hub."""
        await self._execute_mod_action(ctx, ActionType.BAN, user, message, server, hub, reason)

    async def mute(
        self,
        ctx: commands.Context['Bot'],
        duration: str,
        *,
        reason: str = '',
        user: Optional[discord.User] = None,
        message: discord.Message = None,  # type: ignore
        server: discord.Guild = None,  # type: ignore
        hub: Optional[str] = None,
    ):
        """Mute a user or server from a hub."""
        await self._execute_mod_action(ctx, ActionType.MUTE, user, message, server, hub, f'{duration} {reason}'.strip())

    async def warn(
        self,
        ctx: commands.Context['Bot'],
        *,
        reason: str = '',
        user: Optional[discord.User] = None,
        message: discord.Message = None,  # type: ignore
        server: discord.Guild = None,  # type: ignore
        hub: Optional[str] = None,
    ):
        """Warn a user or server in a hub."""
        await self._execute_mod_action(ctx, ActionType.WARN, user, message, server, hub, reason)

    async def unmute(
        self,
        ctx: commands.Context['Bot'],
        *,
        reason: str = '',
        user: Optional[discord.User] = None,
        message: discord.Message = None,  # type: ignore
        server: discord.Guild = None,  # type: ignore
        hub: Optional[str] = None,
    ):
        """Unmute a user or server from a hub."""
        await self._execute_mod_action(ctx, ActionType.UNMUTE, user, message, server, hub, reason)

    async def unban(
        self,
        ctx: commands.Context['Bot'],
        *,
        reason: str = '',
        user: Optional[discord.User] = None,
        message: discord.Message = None,  # type: ignore
        server: discord.Guild = None,  # type: ignore
        hub: Optional[str] = None,
    ):
        """Unban a user or server from a hub."""
        await self._execute_mod_action(ctx, ActionType.UNBAN, user, message, server, hub, reason)

    async def _execute_mod_action(
        self,
        ctx: commands.Context['Bot'],
        action: ActionType,
        user: Optional[discord.User],
        message: Optional[discord.Message],
        server: Optional[discord.Guild],
        hub_name: Optional[str],
        args: str,
    ):
        """Execute a moderation action."""
        await ctx.defer()

        message = await get_message_context(ctx, message)

        if message:
            await self.cog.handle_message_mod_action(ctx, action, message, args)
            return

        if user or server:
            if not hub_name:
                user_hubs = await get_user_moderated_hubs(self.bot, str(ctx.author.id))
                if not user_hubs:
                    return await ctx.send(
                        f'{self.bot.emotes.x_icon} '
                        + t('responses.moderation.errors.noModeratedHubs', locale=self.locale),
                        ephemeral=True,
                    )

                await self._handle_target_panel_with_action(ctx, user, server, user_hubs, action, args)
                return

            selected_hub = await validate_and_get_hub(self.bot, ctx, hub_name, self.locale)
            if not selected_hub:
                return

            target = ModerationTarget(user=user, server=server)
            await self.cog.execute_direct_action(ctx, action, target, selected_hub, args)
            return

        await self.cog.handle_no_target_error(ctx)

    async def _handle_target_panel_with_action(
        self,
        ctx: commands.Context,
        user: Optional[discord.User],
        server: Optional[discord.Guild],
        user_hubs,
        action: ActionType,
        args: str,
    ):
        """Handle target panel with action when hub is not specified."""
        if user and server:
            embed = discord.Embed(
                title=t('ui.common.titles.error', locale=self.locale),
                description=f'{self.bot.emotes.x_icon} {t("responses.moderation.target.both", locale=self.locale)}',
                color=discord.Color.red(),
            )
            return await ctx.send(embed=embed, ephemeral=True)

        view = ActionHubSelectionView(
            self.bot,
            ctx.author,
            user,
            server,
            user_hubs,
            action.value,
            args,
            self.locale,
        )

        embed = discord.Embed(
            title=t('ui.moderation.hubSelection.title', locale=self.locale),
            description=t('ui.moderation.hubSelection.description', locale=self.locale),
            color=self.cog.constants.color,
        )

        if user:
            embed.add_field(
                name=t('ui.moderation.targetSelection.userField', locale=self.locale),
                value=f'{user.mention} (`{user.id}`)',
                inline=True,
            )
        if server:
            embed.add_field(
                name=t('ui.moderation.targetSelection.serverField', locale=self.locale),
                value=f'**{server.name}** (`{server.id}`)',
                inline=True,
            )

        action_name = action.value.title()
        embed.add_field(
            name='Action',
            value=f'**{action_name}**',
            inline=True,
        )

        embed.add_field(
            name=t('ui.moderation.hubSelection.fieldHubLabel', locale=self.locale),
            value=t('ui.moderation.hubSelection.fieldHubPrompt', locale=self.locale),
            inline=False,
        )

        await ctx.send(embed=embed, view=view)
