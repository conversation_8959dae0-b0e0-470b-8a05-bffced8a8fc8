from dataclasses import dataclass
from datetime import datetime
from typing import TYPE_CHECKING, Optional

import discord
from discord.ui import <PERSON><PERSON>, button
from discord.ui import Modal, TextInput
from sqlalchemy import select

from utils.modules.core.db.models import (
    Appeal,
    AppealStatus,
    Hub,
    Infraction,
)
from utils.modules.core.i18n import t
from utils.utils import load_user_locale

from .utils import DEFAULT_VIEW_TIMEOUT, MAX_REASON_LENGTH, BaseModerationView, ModerationContext

if TYPE_CHECKING:
    from main import Bot


class AppealActionView(BaseModerationView):
    """Appeal actions presented in the Appeals log channel."""

    def __init__(self, bot: 'Bot', appeal_id: str, locale: str = 'en'):
        context = ModerationContext(
            bot=bot,
            moderator=None,  # Appeals don't have a specific moderator initially # pyright: ignore[reportArgumentType]
            target_user=None,
            target_server=None,
            target_message=None,
            selected_hub=None,  # Will be set when needed
            locale=locale,
        )
        super().__init__(context, timeout=None)
        self.appeal_handler = AppealHandler(bot)

        # Set custom IDs for persistence
        self.accept_custom_id = f'appeal:accept:{appeal_id}'
        self.reject_custom_id = f'appeal:reject:{appeal_id}'
        self.view_infractions_custom_id = f'appeal:view_infractions:{appeal_id}'

        self.accept_appeal_button.custom_id = self.accept_custom_id
        self.reject_appeal_button.custom_id = self.reject_custom_id
        self.view_infractions_button.custom_id = self.view_infractions_custom_id

        # Emotes
        self.accept_appeal_button.emoji = self.bot.emotes.tick
        self.reject_appeal_button.emoji = self.bot.emotes.x_icon
        self.view_infractions_button.emoji = self.bot.emotes.hammer_icon

    @button(label='Accept Appeal', style=discord.ButtonStyle.success, row=0)
    async def accept_appeal_button(self, interaction: discord.Interaction['Bot'], button: Button):
        locale = await load_user_locale(interaction)
        custom_id = interaction.data.get('custom_id', '') if interaction.data else ''
        appeal_id = self.appeal_handler.parse_appeal_button_id(custom_id)
        if not appeal_id:
            await self.send_error(interaction, t('responses.errors.missingAppealReference', locale))
            return
        modal = AppealDecisionModal(self, appeal_id, decision='accept', locale=locale)
        await interaction.response.send_modal(modal)

    @button(label='Reject Appeal', style=discord.ButtonStyle.danger, row=0)
    async def reject_appeal_button(self, interaction: discord.Interaction['Bot'], button: Button):
        locale = await load_user_locale(interaction)
        custom_id = interaction.data.get('custom_id', '') if interaction.data else ''
        appeal_id = self.appeal_handler.parse_appeal_button_id(custom_id)
        if not appeal_id:
            await self.send_error(interaction, t('responses.errors.missingAppealReference', locale))
            return
        modal = AppealDecisionModal(self, appeal_id, decision='reject', locale=locale)
        await interaction.response.send_modal(modal)

    @button(label='View User Infractions', style=discord.ButtonStyle.secondary, row=1)
    async def view_infractions_button(self, interaction: discord.Interaction['Bot'], button: Button):
        custom_id = interaction.data.get('custom_id', '') if interaction.data else ''
        appeal_id = self.appeal_handler.parse_appeal_button_id(custom_id)
        locale = await load_user_locale(interaction)
        if not appeal_id:
            await self.send_error(interaction, t('responses.errors.missingAppealReference', locale))
            return

        context = await self.appeal_handler.fetch_appeal_context(appeal_id)
        if not context:
            await self.send_error(interaction, t('responses.appeal.errors.notFoundOrDeleted', locale))
            return

        # Fetch recent infractions for the user (last 10)
        user_id = context.user_id
        async with self.bot.db.get_session() as session:
            stmt = select(Infraction).where(Infraction.userId == user_id).order_by(Infraction.createdAt.desc()).limit(10)
            res = await session.execute(stmt)
            infractions = list(res.scalars().all())

        embed = discord.Embed(
            title=t('ui.appeal.viewInfractions.title', locale),
            color=discord.Color.blurple(),
        )
        if not infractions:
            embed.description = t('ui.appeal.viewInfractions.empty', locale)
        else:
            for inf in infractions:
                created_ts = int(inf.createdAt.timestamp()) if inf.createdAt else None
                created_str = f'<t:{created_ts}:R>' if created_ts else t('responses.common.unknown', locale)
                reason = inf.reason or t('responses.appeal.constants.noReason', locale)
                if len(reason) > 120:
                    reason = reason[:117] + '...'
                embed.add_field(
                    name=f'{inf.type.name.title()} — {inf.status.name.title()}',
                    value=f'{t("ui.common.labels.date", locale)}: {created_str}\n{t("ui.moderation.modal.reason.label", locale)}: {reason}',
                    inline=False,
                )

        # Always ephemeral to the moderator clicking
        if interaction.response.is_done():
            await interaction.followup.send(embed=embed, ephemeral=True)
        else:
            await interaction.response.send_message(embed=embed, ephemeral=True)

    async def process_decision(
        self,
        interaction: discord.Interaction['Bot'],
        appeal_id: str,
        decision: str,
        reason: str,
        locale: str,
    ) -> None:
        # Validate appeal exists
        context = await self.appeal_handler.fetch_appeal_context(appeal_id)
        if not context:
            await self.send_error(interaction, t('responses.appeal.errors.notFoundOrDeleted', locale))
            return

        # Update status
        new_status = AppealStatus.ACCEPTED if decision == 'accept' else AppealStatus.REJECTED
        success = await self.appeal_handler.update_appeal_status(appeal_id, new_status)
        if not success:
            await self.send_error(interaction, t('responses.appeal.errors.updateFailed', locale))
            return

        # Update the view (disable both primary buttons and annotate who acted)
        new_view = AppealActionView(self.bot, appeal_id=appeal_id)
        actor = interaction.user
        if new_status == AppealStatus.ACCEPTED:
            new_view.accept_appeal_button.label = t('ui.appeal.status.acceptedBy', locale, name=actor.name)
            new_view.accept_appeal_button.disabled = True
            new_view.reject_appeal_button.disabled = True
        else:
            new_view.reject_appeal_button.label = t('ui.appeal.status.rejectedBy', locale, name=actor.name)
            new_view.reject_appeal_button.disabled = True
            new_view.accept_appeal_button.disabled = True

        await interaction.response.edit_message(view=new_view)

        # DM the appealing user with the decision
        try:
            user_obj = self.bot.get_user(int(context.user_id)) or await self.bot.fetch_user(int(context.user_id))
            if user_obj:
                hub_name = None
                try:
                    # Fetch hub name to personalize message
                    async with self.bot.db.get_session() as session:
                        hub = await session.get(Hub, context.hub_id)
                        hub_name = hub.name if hub else None
                except Exception:
                    hub_name = None

                hub_part = f' {hub_name}' if hub_name else ''
                if new_status == AppealStatus.ACCEPTED:
                    dm = t('responses.appeal.dm.accepted', locale, hubName=hub_part)
                else:
                    dm = t('responses.appeal.dm.declined', locale, hubName=hub_part)
                if reason:
                    dm += '\n\n' + t('responses.appeal.dm.moderatorNote', locale, reason=reason)
                await user_obj.send(dm)

        except Exception:
            pass


@dataclass
class AppealContext:
    appeal_id: str
    user_id: str
    status: AppealStatus
    infraction_id: str
    hub_id: str
    infraction_type: str
    infraction_reason: str | None
    infraction_created_at: datetime


class AppealHandler:
    """Handler for appeal-related operations."""

    def __init__(self, bot: 'Bot'):
        self.bot = bot

    def parse_appeal_button_id(self, custom_id: str) -> Optional[str]:
        parts = custom_id.split(':')
        if len(parts) != 3 or parts[0] != 'appeal':
            return None
        return parts[2] if parts[2] and parts[2] != '0' else None

    async def fetch_appeal_context(self, appeal_id: str) -> Optional[AppealContext]:
        """Fetch appeal and related infraction context from DB."""
        async with self.bot.db.get_session() as session:
            stmt = (
                select(
                    Appeal.id,
                    Appeal.userId,
                    Appeal.status,
                    Appeal.infractionId,
                    Infraction.hubId,
                    Infraction.type,
                    Infraction.reason,
                    Infraction.createdAt,
                )
                .join(Infraction, Appeal.infractionId == Infraction.id)
                .where(Appeal.id == appeal_id)
            )
            result = await session.execute(stmt)
            row = result.first()
            if not row:
                return None
            return AppealContext(
                appeal_id=row[0],
                user_id=row[1],
                status=row[2],
                infraction_id=row[3],
                hub_id=row[4],
                infraction_type=row[5],
                infraction_reason=row[6],
                infraction_created_at=row[7],
            )

    async def update_appeal_status(self, appeal_id: str, new_status: AppealStatus) -> bool:
        """Update appeal status in database."""
        try:
            async with self.bot.db.get_session() as session:
                stmt = select(Appeal).where(Appeal.id == appeal_id)
                res = await session.execute(stmt)
                appeal = res.scalar_one_or_none()
                if not appeal:
                    return False
                appeal.status = new_status
                await session.commit()
                return True
        except Exception:
            return False


class AppealDecisionModal(Modal, title='Appeal Decision'):
    """Modal to optionally collect decision reasoning."""

    def __init__(self, parent_view: 'AppealActionView', appeal_id: str, decision: str, locale: str):
        super().__init__(timeout=DEFAULT_VIEW_TIMEOUT)
        self.locale = locale
        self.title = t('ui.appeal.actions.decisionTitle', locale=self.locale)
        self.parent_view = parent_view
        self.appeal_id = appeal_id
        self.decision = decision  # 'accept' | 'reject'

        self.reason_input: TextInput = TextInput(
            label=t('ui.appeal.actions.reasonOptional.label', locale=self.locale),
            placeholder=t('ui.appeal.actions.reasonOptional.placeholder', locale=self.locale),
            style=discord.TextStyle.paragraph,
            required=False,
            max_length=MAX_REASON_LENGTH,
        )
        self.add_item(self.reason_input)

    async def on_submit(self, interaction: discord.Interaction['Bot']):  # type: ignore[override]
        try:
            await self.parent_view.process_decision(
                interaction,
                appeal_id=self.appeal_id,
                decision=self.decision,
                reason=(self.reason_input.value or '').strip(),
                locale=self.locale,
            )
        except Exception as e:
            await self.parent_view.send_error(
                interaction,
                t(
                    'responses.appeal.errors.recordFailed',
                    locale=self.locale,
                    error=str(e),
                ),
            )
