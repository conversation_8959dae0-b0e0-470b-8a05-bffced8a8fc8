from typing import TYPE_CHECKING

import discord
from discord.ui import Select

from utils.modules.core.checks import is_interchat_staff_direct
from utils.modules.core.i18n import t
from utils.modules.moderation.ui.views.panels import ModPanelView

if TYPE_CHECKING:
    from main import Bot


class ModActionDropdown(Select):
    def __init__(self, parent_view: 'ModPanelView'):
        self.parent_view = parent_view
        self.context = parent_view.context
        self.locale = parent_view.locale

        try:
            options = self._build_action_options()
        except Exception:
            options = [
                discord.SelectOption(label='Error loading actions', description='Please try again', value='error')
            ]

        super().__init__(
            placeholder=t('ui.moderation.actionSelect.placeholder', locale=self.locale),
            options=options,
            min_values=1,
            max_values=1,
        )

    def _build_action_options(self) -> list[discord.SelectOption]:
        options = []
        emotes = self.parent_view.bot.emotes

        if self.context.target_message:
            options.append(
                discord.SelectOption(
                    emoji=emotes.delete_icon,
                    label=t('ui.moderation.actions.delete.label', locale=self.locale),
                    description=t('ui.moderation.actions.delete.description', locale=self.locale),
                    value='delete',
                )
            )

        options.extend(
            [
                discord.SelectOption(
                    emoji=emotes.alert_icon,
                    label=t('ui.moderation.actions.warn.label', locale=self.locale),
                    description=t('ui.moderation.actions.warn.description', locale=self.locale),
                    value='warn',
                ),
                discord.SelectOption(
                    emoji=emotes.clock_icon,
                    label=t('ui.moderation.actions.mute.label', locale=self.locale),
                    description=t('ui.moderation.actions.mute.description', locale=self.locale),
                    value='mute',
                ),
                discord.SelectOption(
                    emoji=emotes.hammer_icon,
                    label=t('ui.moderation.actions.ban.label', locale=self.locale),
                    description=t('ui.moderation.actions.ban.description', locale=self.locale),
                    value='ban',
                ),
                discord.SelectOption(
                    emoji=getattr(emotes, 'unmute_icon', None) or '🔊',
                    label=t('ui.moderation.actions.unmute.label', locale=self.locale),
                    description=t('ui.moderation.actions.unmute.description', locale=self.locale),
                    value='unmute',
                ),
                discord.SelectOption(
                    emoji=getattr(emotes, 'unban_icon', None) or '♻️',
                    label=t('ui.moderation.actions.unban.label', locale=self.locale),
                    description=t('ui.moderation.actions.unban.description', locale=self.locale),
                    value='unban',
                ),
            ]
        )

        if is_interchat_staff_direct(self.parent_view.bot, self.parent_view.moderator.id):
            options.append(
                discord.SelectOption(
                    emoji=emotes.hammer_icon,
                    label=t('ui.moderation.actions.blacklist.label', locale=self.locale),
                    description=t(
                        'ui.moderation.actions.blacklist.description',
                        locale=self.locale,
                    ),
                    value='blacklist',
                )
            )

        return options

    async def callback(self, interaction: discord.Interaction['Bot']):  # pyright: ignore[reportIncompatibleMethodOverride]
        try:
            if not await self.parent_view.validate_interaction(interaction):
                return

            action = self.values[0]

            if action == 'error':
                await self.parent_view.send_error(interaction, 'Please refresh the panel and try again.')
                return

            await self.parent_view.handle_action_selection(interaction, action)

        except Exception as e:
            await self.parent_view.handle_error(interaction, e, 'Failed to process action selection.')
