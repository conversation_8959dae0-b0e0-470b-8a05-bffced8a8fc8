from typing import TYPE_CHECKING, Optional

import discord

from utils.modules.core.db.models import Hub
from utils.modules.core.i18n import t
from ...services.action_handler import ModerationActionHandler
from .dropdowns import ModActionDropdown
from .modals import ReasonModal
from .utils import BaseModerationView, ModerationContext

if TYPE_CHECKING:
    from main import Bot


class ModPanelView(BaseModerationView):
    def __init__(
        self,
        bot: 'Bot',
        moderator: discord.User | discord.Member,
        target_user: Optional[discord.User | discord.Member],
        target_server: Optional[discord.Guild],
        target_message: Optional[discord.Message],
        selected_hub: Hub,
        locale: str,
    ):
        context = ModerationContext(
            bot=bot,
            moderator=moderator,
            target_user=target_user,
            target_server=target_server,
            target_message=target_message,
            selected_hub=selected_hub,
            locale=locale,
        )

        super().__init__(context)
        self.add_item(ModActionDropdown(self))

    async def handle_action_selection(self, interaction: discord.Interaction['Bot'], action: str):
        try:
            if action == 'delete' and self.context.target_message:
                await self._handle_delete_action(interaction)
                return

            if self.context.has_both_targets:
                await self._show_target_selection_view(interaction, action)
            else:
                await self._open_reason_modal(interaction, action)

        except Exception as e:
            await self.handle_error(interaction, e, 'Failed to process action selection.')

    async def _handle_delete_action(self, interaction: discord.Interaction['Bot']):
        assert self.context.selected_hub is not None

        handler = ModerationActionHandler(
            self.bot,
            self.moderator,
            self.context.selected_hub,
            self.locale,
        )
        await handler.handle_delete_message(interaction, self.context.target_message)

    async def _show_target_selection_view(self, interaction: discord.Interaction['Bot'], action: str):
        assert self.context.target_user is not None
        assert self.context.target_server is not None

        from .selection import TargetSelectionView

        view = TargetSelectionView(
            self.context,
            action,
        )

        embed = discord.Embed(
            title=t('ui.moderation.targetSelection.title', locale=self.locale),
            description=t('ui.moderation.targetSelection.description', locale=self.locale, action=action),
            color=discord.Color.blue(),
        )
        embed.add_field(
            name=t('ui.moderation.targetSelection.userField', locale=self.locale),
            value=f'{self.context.target_user.mention} (`{self.context.target_user.id}`)',
            inline=True,
        )
        embed.add_field(
            name=t('ui.moderation.targetSelection.serverField', locale=self.locale),
            value=f'**{self.context.target_server.name}** (`{self.context.target_server.id}`)',
            inline=True,
        )

        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

    async def _open_reason_modal(
        self,
        interaction: discord.Interaction['Bot'],
        action: str,
        target_type: Optional[str] = None,
    ):
        try:
            modal = ReasonModal(self.context, action, target_type)
            await interaction.response.send_modal(modal)
        except Exception as e:
            await self.handle_error(interaction, e, 'Failed to open reason modal.')
