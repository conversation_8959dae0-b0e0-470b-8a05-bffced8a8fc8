from collections.abc import Sequence
from typing import TYPE_CHECKING, Optional

import discord
from discord.ui import Select, button, Button

from utils.modules.core.db.models import Hub
from utils.modules.core.i18n import t
from utils.modules.core.moderation import mod_panel_embed
from ...services.action_handler import ModerationActionHandler
from ...services.types import ActionType, ModerationTarget
from utils.utils import parse_duration

from .utils import (
    MAX_DESCRIPTION_LENGTH,
    MAX_SELECT_OPTIONS,
    PLACEHOLDER_INFRACTIONS,
    BaseModerationView,
    ModerationContext,
)

if TYPE_CHECKING:
    from main import Bot


class HubSelectionView(BaseModerationView):
    """View for selecting a hub when no message is provided.

    This view is displayed when a moderator has access to multiple hubs
    and needs to select which hub they want to moderate.
    """

    def __init__(
        self,
        bot: 'Bot',
        moderator: discord.User | discord.Member,
        target_user: Optional[discord.User | discord.Member],
        target_server: Optional[discord.Guild],
        target_message: Optional[discord.Message],
        user_hubs: Sequence[Hub],
        locale: str,
    ):
        context = ModerationContext(
            bot=bot,
            moderator=moderator,
            target_user=target_user,
            target_server=target_server,
            target_message=target_message,
            selected_hub=None,  # Will be set when hub is selected
            locale=locale,
        )
        super().__init__(context)
        self.target_user = target_user
        self.target_server = target_server
        self.target_message = target_message
        self.user_hubs = user_hubs

        # Add hub selection dropdown
        self.add_item(HubSelectDropdown(self.user_hubs, self))

    async def update_with_hub(self, interaction: discord.Interaction, selected_hub: Hub):
        """Update the view after a hub is selected - show the mod panel."""
        from .panels import ModPanelView

        # Create the main mod panel
        view = ModPanelView(
            self.bot,
            self.moderator,
            self.target_user,
            self.target_server,
            self.target_message,
            selected_hub,
            self.locale,
        )

        # Create embed
        embed = mod_panel_embed(
            self.bot,
            selected_hub,
            self.target_user,
            self.target_server,
            self.target_message,
            self.target_message.content if self.target_message else '',
            user_infractions=PLACEHOLDER_INFRACTIONS,
            server_infractions=PLACEHOLDER_INFRACTIONS,
            _locale=self.locale,
        )

        await interaction.response.edit_message(embed=embed, view=view)


class ActionHubSelectionView(BaseModerationView):
    def __init__(
        self,
        bot: 'Bot',
        moderator: discord.User | discord.Member,
        user: Optional[discord.User | discord.Member],
        server: Optional[discord.Guild],
        user_hubs: Sequence[Hub],
        action: str,
        args: str,
        locale: str,
    ):
        context = ModerationContext(
            bot=bot,
            moderator=moderator,
            target_user=user,
            target_server=server,
            target_message=None,
            selected_hub=None,  # Will be set when hub is selected
            locale=locale,
        )
        super().__init__(context)
        self.user = user
        self.server = server
        self.user_hubs = list(user_hubs)
        self.action = action
        self.args = args

        options: list[discord.SelectOption] = []
        for hub in self.user_hubs[:MAX_SELECT_OPTIONS]:
            description = hub.shortDescription or t('ui.common.noDescription', locale=locale)
            if len(description) > MAX_DESCRIPTION_LENGTH:
                description = description[:MAX_DESCRIPTION_LENGTH]
            options.append(
                discord.SelectOption(
                    label=hub.name,
                    value=hub.id,
                    description=description,
                )
            )

        select = Select(
            placeholder=t('ui.moderation.hubSelection.fieldHubPrompt', locale=locale),
            options=options,
            min_values=1,
            max_values=1,
        )
        select.callback = self._on_hub_selected
        self.add_item(select)

    def _parse_args_for_target_and_reason(
        self, args: str, action: str
    ) -> tuple[Optional[str], Optional[int], str]:
        """Parse arguments to extract duration (for mute) and reason."""
        if not args:
            return None, None, ''
        parts = args.split()
        duration_ms = None
        i = 0
        if action == 'mute' and i < len(parts):
            try:
                duration_ms = parse_duration(parts[i])
                i += 1
            except ValueError:
                pass
        reason = ' '.join(parts[i:]) if i < len(parts) else None
        return reason, duration_ms, args

    async def _on_hub_selected(self, interaction: discord.Interaction['Bot']):
        if not await self.validate_interaction(interaction):
            return

        selected_hub_id = None
        if isinstance(self.children[0], Select) and self.children[0].values:
            selected_hub_id = self.children[0].values[0]

        selected_hub = next((h for h in self.user_hubs if h.id == selected_hub_id), None)
        if not selected_hub:
            await self.send_error(
                interaction, t('responses.moderation.errors.invalidHubData', self.locale)
            )
            return

        # Build target
        target = ModerationTarget(user=self.user, server=self.server)

        # Parse args
        reason, duration_ms, _ = self._parse_args_for_target_and_reason(self.args, self.action)

        # Validate duration for mute
        if self.action == 'mute' and duration_ms is None:
            await self.send_error(
                interaction,
                'Duration is required for mute actions. Use format like "1d", "2h", "30m".',
            )
            return

        handler = ModerationActionHandler(self.bot, self.moderator, selected_hub, self.locale)

        # Dispatch
        try:
            action_enum = ActionType(self.action)
        except ValueError:
            await self.send_error(
                interaction, t('responses.moderation.errors.unknownAction', self.locale)
            )
            return

        if action_enum in {ActionType.WARN, ActionType.MUTE, ActionType.BAN}:
            await handler.handle_punitive_action(
                interaction, action_enum, target, reason, duration_ms
            )
        elif action_enum in {ActionType.UNMUTE, ActionType.UNBAN}:
            await handler.handle_revoke_action(interaction, action_enum, target)
        else:
            await self.send_error(
                interaction, t('responses.moderation.errors.unsupportedAction', self.locale)
            )


class TargetSelectionView(BaseModerationView):
    """View for selecting between user and server targets."""

    def __init__(
        self,
        context: ModerationContext,
        selected_action: str,
    ):
        super().__init__(context)
        self.selected_action = selected_action

    @button(label='Act on User', style=discord.ButtonStyle.primary, emoji='👤')
    async def select_user(self, interaction: discord.Interaction['Bot'], button: Button):  # noqa: ARG002
        """Select user as the target."""
        try:
            if not await self.validate_interaction(interaction):
                return

            # Import here to avoid circular imports
            from .modals import ReasonModal

            modal = ReasonModal(self.context, self.selected_action, target_type='user')
            await interaction.response.send_modal(modal)

        except Exception as e:
            await self.handle_error(interaction, e, 'Failed to open reason modal for user target.')

    @button(label='Act on Server', style=discord.ButtonStyle.secondary, emoji='🏢')
    async def select_server(self, interaction: discord.Interaction['Bot'], button: Button):  # noqa: ARG002
        """Select server as the target."""
        try:
            if not await self.validate_interaction(interaction):
                return

            from .modals import ReasonModal

            modal = ReasonModal(self.context, self.selected_action, target_type='server')
            await interaction.response.send_modal(modal)

        except Exception as e:
            await self.handle_error(
                interaction, e, 'Failed to open reason modal for server target.'
            )


class HubSelectDropdown(discord.ui.Select):
    def __init__(self, hubs: Sequence[Hub], parent_view: HubSelectionView):
        self.locale = parent_view.locale
        options = []
        seen_ids = set()  # Track hub IDs to prevent duplicates

        for hub in hubs[:MAX_SELECT_OPTIONS]:
            seen_ids.add(hub.id)

            description = hub.shortDescription or t('ui.common.noDescription', locale=self.locale)
            if len(description) > MAX_DESCRIPTION_LENGTH:
                description = description[:MAX_DESCRIPTION_LENGTH]

            options.append(
                discord.SelectOption(
                    label=hub.name,
                    value=hub.id,
                    description=description,
                )
            )

        # Ensure we have at least one option
        if not options:
            options.append(
                discord.SelectOption(
                    label='No hubs available',
                    value='none',
                    description='No hubs available for moderation',
                )
            )

        super().__init__(
            placeholder=t('ui.moderation.hubSelect.placeholder', locale=self.locale),
            options=options,
            min_values=1,
            max_values=1,
        )
        self.hubs = hubs
        self.parent_view = parent_view

    async def callback(self, interaction: discord.Interaction['Bot']):  # type: ignore[override]
        """Handle hub selection from the dropdown."""
        if not await self.parent_view.validate_interaction(interaction):
            return

        selected_hub_id = self.values[0]

        # Handle the "none" option case
        if selected_hub_id == 'none':
            await self.parent_view.send_error(
                interaction,
                'No hubs available for moderation.',
            )
            return

        selected_hub = next((hub for hub in self.hubs if hub.id == selected_hub_id), None)

        if selected_hub:
            await self.parent_view.update_with_hub(interaction, selected_hub)
        else:
            await self.parent_view.send_error(
                interaction,
                t(
                    'responses.moderation.errors.selectedHubNotFound',
                    locale=self.locale,
                ),
            )