from typing import TYPE_CHECKING, Optional

import discord
from discord.ui import Modal, TextInput

from utils.constants import logger
from utils.modules.core.i18n import t
from ...services.action_handler import ModerationActionHandler
from ...services.types import ActionType
from .utils import (
    DEFAULT_VIEW_TIMEOUT,
    MAX_DURATION_LENGTH,
    MAX_REASON_LENGTH,
    MIN_DURATION_LENGTH,
    MIN_REASON_LENGTH,
    ModerationContext,
    ModerationValidator,
    ValidationError,
)

if TYPE_CHECKING:
    pass


class ReasonModal(Modal):
    def __init__(self, context: ModerationContext, action: str, target_type: Optional[str]):
        super().__init__(title=t('ui.moderation.modal.title', context.locale), timeout=DEFAULT_VIEW_TIMEOUT)
        self.context = context
        self.action = action
        self.target_type = target_type
        self.locale = context.locale

        self._setup_reason_input()
        self._setup_duration_input()

    def _setup_reason_input(self):
        reason_required = self.action not in {'unmute', 'unban'}
        optional_suffix = '' if reason_required else ' (optional)'
        placeholder = t(
            'ui.moderation.modal.reason.placeholder',
            locale=self.locale,
            optional=optional_suffix,
        )

        self.reason_input = TextInput(
            label=t('ui.moderation.modal.reason.label', locale=self.locale),
            placeholder=placeholder,
            style=discord.TextStyle.paragraph,
            min_length=0 if not reason_required else MIN_REASON_LENGTH,
            max_length=MAX_REASON_LENGTH,
            required=reason_required,
        )
        self.add_item(self.reason_input)

    def _setup_duration_input(self):
        if self.action == 'mute':
            self.duration_input = TextInput(
                label=t('ui.moderation.modal.duration.label', locale=self.locale),
                placeholder=t('ui.moderation.modal.duration.placeholder', locale=self.locale),
                style=discord.TextStyle.short,
                required=True,
                min_length=MIN_DURATION_LENGTH,
                max_length=MAX_DURATION_LENGTH,
            )
            self.add_item(self.duration_input)
        else:
            self.duration_input: Optional[TextInput] = None

    async def on_submit(self, interaction: discord.Interaction):
        try:
            reason = self.reason_input.value.strip()
            action_enum = ActionType(self.action)

            ModerationValidator.validate_reason(reason, action_enum)

            duration_ms = None
            if self.action == 'mute' and self.duration_input:
                duration_ms = ModerationValidator.validate_duration(self.duration_input.value.strip(), action_enum)

            await self._execute_moderation_action(interaction, reason, duration_ms, action_enum)

        except ValidationError as e:
            await self._send_validation_error(interaction, str(e))
        except ValueError:
            await self._send_validation_error(interaction, 'Invalid action type.')
        except Exception as e:
            await self._send_generic_error(interaction, e)

    async def _send_validation_error(self, interaction: discord.Interaction, message: str):
        embed = discord.Embed(
            title='Validation Error',
            description=f'{self.context.bot.emotes.x_icon} {message}',
            color=discord.Color.red(),
        )
        if not interaction.response.is_done():
            await interaction.response.send_message(embed=embed, ephemeral=True)
        else:
            await interaction.followup.send(embed=embed, ephemeral=True)

    async def _send_generic_error(self, interaction: discord.Interaction, error: Exception):
        embed = discord.Embed(
            title='Error',
            description=f'{self.context.bot.emotes.x_icon} An error occurred while processing your request.',
            color=discord.Color.red(),
        )
        if not interaction.response.is_done():
            await interaction.response.send_message(embed=embed, ephemeral=True)
        else:
            await interaction.followup.send(embed=embed, ephemeral=True)

    async def _execute_moderation_action(
        self,
        interaction: discord.Interaction,
        reason: str,
        duration_ms: Optional[int],
        action_enum: ActionType,
    ):
        target = self.context.create_target(self.target_type)
        ModerationValidator.validate_action_target_compatibility(action_enum, target)

        assert self.context.selected_hub is not None

        handler = ModerationActionHandler(
            self.context.bot,
            self.context.moderator,
            self.context.selected_hub,
            self.locale,
        )

        if action_enum == ActionType.DELETE and self.context.target_message:
            await handler.handle_delete_message(interaction, self.context.target_message, reason=reason or None)
        elif action_enum in {ActionType.WARN, ActionType.MUTE, ActionType.BAN}:
            await handler.handle_punitive_action(
                interaction, action_enum, target, reason or None, duration_ms=duration_ms
            )
        elif action_enum in {ActionType.UNMUTE, ActionType.UNBAN}:
            await handler.handle_revoke_action(interaction, action_enum, target)
        elif action_enum == ActionType.BLACKLIST:
            await handler.handle_global_blacklist_action(interaction, target, reason or None, duration_ms=duration_ms)
        else:
            await self._send_validation_error(interaction, 'Unsupported action type.')

    async def on_error(self, interaction: discord.Interaction, error: Exception) -> None:
        logger.error(f'Error in reason modal: {error}', exc_info=error)
        await self._send_generic_error(interaction, error)
