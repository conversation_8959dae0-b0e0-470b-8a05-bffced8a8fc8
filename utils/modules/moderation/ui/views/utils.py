from typing import TYPE_CHECKING, Optional, Protocol, runtime_checkable
from dataclasses import dataclass


import discord
from discord.ui import View

from utils.modules.common.error_utils import (
    send_error_message,
    send_success_message,
    handle_interaction_error,
)
from utils.modules.core.checks import interaction_check
from ...services.types import ActionType, ModerationTarget

from utils.utils import parse_duration

if TYPE_CHECKING:
    from main import Bot

from utils.modules.core.db.models import Hub

# Constants
MAX_SELECT_OPTIONS = 25
DEFAULT_VIEW_TIMEOUT = 300
MAX_REASON_LENGTH = 400
MIN_REASON_LENGTH = 3
MAX_DURATION_LENGTH = 20
MIN_DURATION_LENGTH = 1
MAX_DESCRIPTION_LENGTH = 100

PLACEHOLDER_REPUTATION = 0
PLACEHOLDER_INFRACTIONS = 0


class ValidationError(Exception):
    """Custom exception for validation errors."""

    def __init__(self, message: str, field: Optional[str] = None):
        super().__init__(message)
        self.field = field


@dataclass
class ModerationContext:
    bot: 'Bot'
    moderator: discord.User | discord.Member
    target_user: Optional[discord.User | discord.Member]
    target_server: Optional[discord.Guild]
    target_message: Optional[discord.Message]
    selected_hub: Optional['Hub']
    locale: str

    @property
    def has_both_targets(self) -> bool:
        """Check if both user and server targets are available."""
        return self.target_user is not None and self.target_server is not None

    @property
    def has_any_target(self) -> bool:
        """Check if any target is available."""
        return self.target_user is not None or self.target_server is not None

    def create_target(self, target_type: Optional[str] = None) -> ModerationTarget:
        """Create a ModerationTarget based on the specified type or available targets."""
        if target_type == 'user':
            return ModerationTarget(user=self.target_user, server=None)
        elif target_type == 'server':
            return ModerationTarget(user=None, server=self.target_server)
        else:
            # Auto-resolve based on what's available
            if self.target_user and not self.target_server:
                return ModerationTarget(user=self.target_user, server=None)
            elif self.target_server and not self.target_user:
                return ModerationTarget(user=None, server=self.target_server)
            else:
                # Both available, default to user
                return ModerationTarget(user=self.target_user, server=self.target_server)


@runtime_checkable
class ModerationActionHandler(Protocol):
    async def handle_action(
        self,
        interaction: discord.Interaction,
        action: ActionType,
        target: ModerationTarget,
        reason: Optional[str] = None,
        duration_ms: Optional[int] = None,
    ) -> None:
        """Handle a moderation action."""
        ...


class ModerationValidator:
    @staticmethod
    def validate_reason(reason: str, action: ActionType) -> None:
        """Validate reason based on action type."""
        reason = reason.strip()

        # Check if reason is required for this action
        reason_required = action not in {ActionType.UNMUTE, ActionType.UNBAN}

        if reason_required and len(reason) < MIN_REASON_LENGTH:
            raise ValidationError(
                f'Reason must be at least {MIN_REASON_LENGTH} characters long.',
                field='reason',
            )

        if len(reason) > MAX_REASON_LENGTH:
            raise ValidationError(
                f'Reason must be no more than {MAX_REASON_LENGTH} characters long.',
                field='reason',
            )

    @staticmethod
    def validate_duration(duration_str: str, action: ActionType) -> int:
        """Validate and parse duration string."""
        if action != ActionType.MUTE:
            raise ValidationError(
                'Duration is only needed for mute actions.',
                field='duration',
            )

        duration_str = duration_str.strip()
        if len(duration_str) < MIN_DURATION_LENGTH:
            raise ValidationError(
                'Duration is too short.',
                field='duration',
            )

        if len(duration_str) > MAX_DURATION_LENGTH:
            raise ValidationError(
                'Duration is too long.',
                field='duration',
            )

        try:
            duration_ms = parse_duration(duration_str.lower())
            return duration_ms
        except ValueError:
            raise ValidationError(
                "Invalid duration format. Use formats like '1h', '30m', '2d'.",
                field='duration',
            )

    @staticmethod
    def validate_action_target_compatibility(action: ActionType, target: ModerationTarget) -> None:
        """Validate that the action is compatible with the target type."""
        if not target.is_user and not target.is_server:
            raise ValidationError('No valid target specified for this action.', field='target')

        # Some actions might have specific target requirements in the future
        # This is where we'd add those validations @bread


class BaseModerationView(View):
    def __init__(
        self,
        context: ModerationContext,
        timeout: Optional[float] = DEFAULT_VIEW_TIMEOUT,
    ):
        super().__init__(timeout=timeout)
        self.context = context
        self.bot = context.bot
        self.moderator = context.moderator
        self.locale = context.locale
        self.constants = context.bot.constants

    async def validate_interaction(
        self, interaction: discord.Interaction['Bot'], expected_user: Optional[discord.User] = None
    ) -> bool:
        """Validate that the interaction is from the expected user."""
        target_user = expected_user or self.moderator
        return await interaction_check(interaction, target_user, interaction.user)

    async def send_error(self, interaction: discord.Interaction['Bot'], message: str) -> None:
        await send_error_message(interaction, message, ephemeral=True)

    async def send_success(self, interaction: discord.Interaction['Bot'], message: str) -> None:
        await send_success_message(interaction, message, ephemeral=True)

    async def handle_error(
        self,
        interaction: discord.Interaction['Bot'],
        error: Exception,
        user_message: Optional[str] = None,
    ) -> None:
        if isinstance(error, ValidationError):
            await self.send_error(interaction, str(error))
        else:
            # TODO: Localize
            await handle_interaction_error(
                interaction,
                error,
                user_message=user_message or 'An error occurred while processing your request.',
            )
