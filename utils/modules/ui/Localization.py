from __future__ import annotations

from typing import TYPE_CHECKING, Any, Dict, List, Optional

import discord
from typing_extensions import deprecated

from utils.modules.core.i18n import t

if TYPE_CHECKING:
    from utils.modules.core.i18n import <PERSON><PERSON><PERSON><PERSON>


@deprecated('Make your own embed that uses t() directly. This will be removed in future versions.')
def create_embed(
    locale: str,
    title_key: LocaleKey,
    description_key: LocaleKey,
    fields: Optional[List[Dict[str, Any]]] = None,
    footer_key: Optional[LocaleKey] = None,
    author_key: Optional[LocaleKey] = None,
    thumbnail: Optional[str] = None,
    image: Optional[str] = None,
    color: int = 0x9172D8,
    **format_vars,
) -> discord.Embed:
    """
    Creates a discord.Embed using localized text.
    """
    embed = discord.Embed(color=color)

    if title_key:
        embed.title = t(title_key, locale, **format_vars)

    if description_key:
        embed.description = t(description_key, locale, **format_vars)

    if fields:
        for field in fields:
            name = t(field['name_key'], locale, **format_vars)
            value = t(field['value_key'], locale, **format_vars)
            inline = bool(field.get('inline', False))
            embed.add_field(name=name, value=value, inline=inline)

    if footer_key:
        embed.set_footer(text=t(footer_key, locale, **format_vars))

    if author_key:
        embed.set_author(name=t(author_key, locale, **format_vars))

    if thumbnail:
        embed.set_thumbnail(url=thumbnail)

    if image:
        embed.set_image(url=image)

    return embed
