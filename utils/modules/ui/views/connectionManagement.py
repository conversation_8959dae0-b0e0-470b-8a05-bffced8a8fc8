import discord
from discord import ui

from sqlalchemy import select

from utils.modules.core.checks import interaction_check
from utils.modules.core.i18n import t
from utils.modules.ui.views.BaseView import BaseView
from utils.modules.core.db.models import Connection
from utils.modules.core.webhookCore import fix_connections
from utils.constants import logger

from typing import TYPE_CHECKING, Sequence

if TYPE_CHECKING:
    from main import Bot


class ActionView(BaseView):
    def __init__(
        self,
        bot: 'Bot',
        user: discord.User | discord.Member,
        data: Connection,
        connections,
        hubName,
        locale,
    ):
        super().__init__(bot, user, timeout=300)
        self.bot = bot
        self.user = user
        self.data = data
        self.locale = locale
        self.connections = connections
        self.hubName = hubName

        self.state = 'None'
        self.get_state()
        self.setup_back()

        self.action_select = ui.Select(
            placeholder='Select an action',
            options=[
                discord.SelectOption(emoji=bot.emotes.delete_icon, label='Remove Connection', value='rmc'),
                discord.SelectOption(
                    emoji=bot.emotes.outgoing_icon,
                    label=f'{self.state} Broadcast',
                    value='state',
                ),
            ],
            min_values=1,
            max_values=1,
        )
        self.action_select.callback = self.action_on_submit
        self.add_item(self.action_select)

    def setup_back(self):
        fields = []
        for con, hub_name in self.connections:
            fields.append(
                {
                    'name': f'<#{con.channelId}>',
                    'value': (
                        f'> **{t("responses.common.hub", self.locale)}:** {hub_name}\n'
                        f'> **{t("commands.connections.fields.lastActive", self.locale)}:** <t:{int(con.lastActive.timestamp())}:R>'
                    ),
                    'inline': True,
                }
            )

        self.add_back_button(
            ConnectionView(self.bot, self.user, self.connections, self.locale),
            embed_title=t('commands.connections.title', locale=self.locale),
            embed_description=t('commands.connections.description', locale=self.locale),
            fields=fields,
            row=2,
        )

    def get_state(self):
        self.state = 'Start' if not self.data.connected else 'Pause'

    def fetch_message(self, message: discord.Message):
        self.message = message

    async def refresh_view(self, interaction: discord.Interaction['Bot']):
        self.action_select.options[1].emoji = (
            self.bot.emotes.outgoing_icon if self.state != 'Pause' else self.bot.emotes.hangup_icon
        )
        self.action_select.options[1].label = f'{self.state} Broadcast'

        embed = discord.Embed(
            title=self.hubName,
            description=t('commands.connections.selected.description', self.locale),
            color=self.bot.constants.color,
        )

        embed.add_field(
            name=self.hubName,
            value=(
                f'> {self.bot.emotes.link_icon} **Channel:** <#{self.data.channelId}>\n'
                f'> {self.bot.emotes.call_icon if self.data.connected else self.bot.emotes.hangup_icon} **State:** {"Active" if self.data.connected else "Paused"}\n'
                f'> {self.bot.emotes.clock_icon} **Last Active:** <t:{int(self.data.lastActive.timestamp())}:R>'
            ),
            inline=False,
        )

        await self.message.edit(embed=embed, view=self)

    async def action_on_submit(self, interaction: discord.Interaction['Bot']):
        await interaction.response.defer(ephemeral=True)
        await interaction_check(interaction, self.user, interaction.user)

        match self.action_select.values[0]:
            case 'rmc':
                if not interaction.guild:
                    logger.warning('action_on_submit: Not in guild')
                    return

                async with self.bot.db.get_session() as session:
                    stmt = select(Connection).where(
                        Connection.serverId == str(interaction.guild.id),
                        Connection.hubId == self.data.hubId,
                    )
                    result = await session.scalar(stmt)

                    if result:
                        await session.delete(result)
                        await session.commit()

                embed = discord.Embed(
                    title='Success!',
                    description=f'{self.bot.emotes.tick} Disconnected from Hub.',
                    color=discord.Color.green(),
                )
                await interaction.edit_original_response(embed=embed, view=self)

            case 'state':
                if not interaction.guild:
                    logger.warning('action_on_submit: Not in guild')
                    return

                async with self.bot.db.get_session() as session:
                    stmt = (
                        select(Connection)
                        .where(
                            Connection.serverId == str(interaction.guild.id),
                            Connection.hubId == self.data.hubId,
                        )
                        .limit(1)
                    )
                    result = await session.scalar(stmt)

                    if not result:
                        embed = discord.Embed(
                            title='Error',
                            description='Connection not found in the database.',
                            color=discord.Color.red(),
                        )
                        await interaction.followup.send(embed=embed, ephemeral=True)
                        return

                    result.connected = not result.connected
                    await session.commit()

                self.data.connected = result.connected if result else self.data.connected

                embed = discord.Embed(title='Success!')

                self.get_state()
                if result and result.connected:
                    state_msg = t('commands.connections.selected.state.enabled', self.locale)
                else:
                    state_msg = t('commands.connections.selected.state.disabled', self.locale)

                embed = discord.Embed(
                    title='Success!',
                    description=f'{self.bot.emotes.tick} {state_msg}',
                    color=discord.Color.green(),
                )
                await self.refresh_view(interaction)
                await interaction.followup.send(embed=embed, ephemeral=True)


class ConnectionView(BaseView):
    def __init__(
        self,
        bot: 'Bot',
        user: discord.User | discord.Member,
        data: Sequence[tuple[Connection, str]],
        locale,
    ):
        super().__init__(bot, user, timeout=300)
        self.bot = bot
        self.user = user
        self.data = data
        self.locale = locale

        self.id_to_hub: dict[str, str] = {str(con.id): hub_name for con, hub_name in data}

        self.select_menu = ui.Select(
            placeholder='Select a connection',
            options=[discord.SelectOption(label=hub_name, value=str(con.id)) for con, hub_name in data],
            min_values=1,
            max_values=1,
        )
        self.select_menu.callback = self.on_submit  # pyright: ignore[reportAttributeAccessIssue]
        self.add_item(self.select_menu)

        self.setup_button()

    def setup_button(self):
        self.fix_cons.emoji = self.bot.emotes.hammer_icon

    @ui.button(label='Fix Connections', style=discord.ButtonStyle.red, row=2)
    async def fix_cons(self, interaction: discord.Interaction['Bot'], button: ui.Button):
        await interaction.response.defer(ephemeral=True)
        await interaction_check(interaction, self.user, interaction.user)

        if not interaction.guild:
            await interaction.followup.send(
                embed=discord.Embed(
                    title='Error',
                    description='This command can only be used in a server.',
                    color=discord.Color.red(),
                ),
                ephemeral=True,
            )
            return

        fixed_connections, user_error = await fix_connections(self.bot, interaction.guild, self.locale)
        embed = discord.Embed(
            title='Connection Validation',
            description='All connections within this guild have been validated. See below for more information.',
            color=discord.Color.green() if fixed_connections and not user_error else discord.Color.orange(),
        )
        for conn, status in fixed_connections:
            embed.add_field(name=f'<#{conn.channelId}>', value=status, inline=True)
        for conn, status in user_error:
            embed.add_field(name=f'<#{conn.channelId}>', value=status, inline=True)

        await interaction.followup.send(embed=embed, ephemeral=True)

    async def on_submit(self, interaction: discord.Interaction['Bot']):
        await interaction.response.defer(ephemeral=True)
        await interaction_check(interaction, self.user, interaction.user)

        selected_value = self.select_menu.values[0]
        hub_name = self.id_to_hub.get(selected_value, 'Unknown Hub')

        selected_con = next((con for con, _ in self.data if str(con.id) == selected_value))

        embed = discord.Embed(
            title=hub_name,
            description=t('commands.connections.selected.description', self.locale),
            color=self.bot.constants.color,
        )
        embed.add_field(
            name=hub_name,
            value=(
                f'> {self.bot.emotes.link_icon} **{t("commands.connections.selected.fields.broadcastChannel", self.locale)}:** <#{selected_con.channelId}>\n'
                f'> {self.bot.emotes.call_icon if selected_con.connected else self.bot.emotes.hangup_icon} **{t("commands.connections.selected.fields.connectionState", self.locale)}:** {"Active" if selected_con.connected else "Paused"}\n'
                f'> {self.bot.emotes.clock_icon} **{t("commands.connections.selected.fields.lastActive", self.locale)}:** <t:{int(selected_con.lastActive.timestamp())}:R>'
            ),
            inline=False,
        )

        view = ActionView(self.bot, self.user, selected_con, self.data, hub_name, self.locale)
        message = await interaction.edit_original_response(embed=embed, view=view)
        view.fetch_message(message)
