from typing import TYPE_CHECKING, Optional

import discord

from utils.modules.core.db.models import Hu<PERSON>, PatternMatchType
from utils.modules.hub.constants import HubPermissionLevel
from utils.modules.services.profanityConfigService import ProfanityConfigService
from utils.modules.ui.CustomModal import CustomModal
from utils.modules.ui.views.hubConfig.profanityActionSelectionView import (
    ProfanityActionSelectionView,
)
from utils.modules.ui.views.hubConfig.utils import BaseHubView

if TYPE_CHECKING:
    from main import Bot


class ProfanitySettingsView(BaseHubView):
    def __init__(
        self,
        bot: 'Bot',
        user: discord.User | discord.Member,
        hub: Hub,
        permission: HubPermissionLevel,
        locale: str,
        timeout: int = 300,
    ):
        super().__init__(bot, user, hub, permission, locale, timeout)
        self.bot = bot
        self.user = user
        self.hub = hub
        self.selected_rule_id: Optional[str] = None
        self.last_selected_action: Optional[str] = None  # Track last selected action
        self.rules = []
        self.patterns = []
        self.whitelists = []
        self._action_select_added = False
        self.emotes = self.bot.emotes

    async def load_data(self):
        async with self.bot.db.get_session() as session:
            svc = ProfanityConfigService(session)
            self.rules = await svc.list_rules(self.hub.id)

        rule_options = []
        if self.rules:
            rule_options = [
                discord.SelectOption(
                    label=rule.name,
                    description=f'{"✅ Enabled" if rule.enabled else "❌ Disabled"} • {len(rule.patterns or [])} patterns',
                    value=rule.id,
                    emoji=self.emotes.changelog_icon,
                    default=rule.id == self.selected_rule_id,  # Mark selected rule as default
                )
                for rule in self.rules
            ]
            self.rule_select.disabled = False
        else:
            rule_options = [discord.SelectOption(label='No rules created yet', value='none')]
            self.rule_select.disabled = True

        self.rule_select.options = rule_options

        # Update placeholder to show selected rule if any
        if self.selected_rule_id and self.rules:
            selected_rule = next((r for r in self.rules if r.id == self.selected_rule_id), None)
            if selected_rule:
                self.rule_select.placeholder = f'{selected_rule.name} (selected)'
            else:
                self.rule_select.placeholder = 'Select a Rule'
        else:
            self.rule_select.placeholder = 'Select a Rule'

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        return interaction.user.id == self.user.id

    def create_embed(self) -> discord.Embed:
        desc = (
            f'### {self.emotes.shield_alert_icon} Anti-Swear Configuration\n'
            'Configure anti-swear rules to automatically moderate messages in your hub.\n\n'
        )

        if self.rules:
            desc += f'{self.emotes.changelog_icon} **{len(self.rules)} rules configured**\n'
            enabled_count = sum(1 for r in self.rules if r.enabled)
            desc += f'{self.emotes.tick} **{enabled_count} active**\n\n'
            desc += '**Select a rule below to manage its patterns and whitelist**'
        else:
            desc += 'No anti-swear rules configured yet. Create your first rule below!'

        embed = discord.Embed(description=desc, color=self.bot.constants.color)
        embed.set_footer(text=f'Hub: {self.hub.name}')
        return embed

    def _create_rule_detail_embed(self) -> discord.Embed:
        """Create a detailed embed for the currently selected rule."""
        # Fallback to general embed if nothing selected
        if not self.selected_rule_id:
            return self.create_embed()

        # Find selected rule
        rule = next((r for r in self.rules if r.id == self.selected_rule_id), None)
        if not rule:
            return self.create_embed()

        # Build description
        status = f'{self.emotes.tick} Enabled' if rule.enabled else f'{self.emotes.cross} Disabled'
        desc = f'### {self.emotes.rules_icon} {rule.name} • {status}\n'

        # Actions
        actions_text = ', '.join(a.value for a in (rule.actions or [])) or '—'
        desc += f'{self.emotes.dot} Actions: {actions_text}\n'

        # Configuration
        conf_lines = []
        if rule.muteDurationMinutes is not None:
            conf_lines.append(f'Mute duration: {rule.muteDurationMinutes} min')
        if conf_lines:
            desc += f'{self.emotes.dot} Config: ' + ' • '.join(conf_lines) + '\n'

        # Patterns (use loaded self.patterns)
        if self.patterns:
            desc += f'\n{self.emotes.hash_icon} Patterns ({len(self.patterns)}):\n```'
            for p in self.patterns:
                desc += f'{p.pattern}, '
            desc = desc.removesuffix(', ') + '```'
        else:
            desc += f'\n{self.emotes.hash_icon} Patterns: None configured\n'

        # Whitelist summary
        if self.whitelists:
            sample = ', '.join(w.word for w in self.whitelists[:5])
            more = f' (+{len(self.whitelists) - 5} more)' if len(self.whitelists) > 5 else ''
            desc += f'{self.emotes.list_icon} Whitelist: {len(self.whitelists)} words • {sample}{more}\n'
        else:
            desc += f'{self.emotes.list_icon} Whitelist: None\n'

        embed = discord.Embed(description=desc, color=self.bot.constants.color)
        embed.set_footer(text=f'Hub: {self.hub.name}')
        return embed

    @discord.ui.select(
        placeholder='{self.emotes.changelog_icon} Select a Rule',
        disabled=True,
        row=0,
    )
    async def rule_select(self, interaction: discord.Interaction['Bot'], select: discord.ui.Select):
        if not await self.interaction_check(interaction):
            return

        if select.values[0] == 'none':
            return await interaction.response.defer()

        self.selected_rule_id = select.values[0]
        self.last_selected_action = None  # Reset action selection when rule changes

        async with self.bot.db.get_session() as session:
            svc = ProfanityConfigService(session)
            self.patterns = await svc.list_patterns(self.selected_rule_id)
            self.whitelists = await svc.list_whitelist(self.selected_rule_id)

        # Get current rule for action description
        current_rule = next(r for r in self.rules if r.id == self.selected_rule_id)
        actions_text = ', '.join(a.value for a in (current_rule.actions or [])) or 'None'

        # TODO: Localize
        action_options = [
            discord.SelectOption(
                label='Edit Actions',
                description=f'Current: {actions_text[:50]}{"..." if len(actions_text) > 50 else ""}',
                value='edit_actions',
                emoji=self.emotes.gear_icon,
                default=self.last_selected_action == 'edit_actions'
                if self.last_selected_action
                else False,
            ),
            discord.SelectOption(
                label='Patterns',
                description=f'{len(self.patterns)} patterns configured',
                value='patterns',
                emoji=self.emotes.hash_icon,
                default=self.last_selected_action == 'patterns'
                if self.last_selected_action
                else False,
            ),
            discord.SelectOption(
                label='Whitelist',
                description=f'{len(self.whitelists)} safe words',
                value='whitelist',
                emoji=self.emotes.list_icon,
                default=self.last_selected_action == 'whitelist'
                if self.last_selected_action
                else False,
            ),
            discord.SelectOption(
                label='Toggle Enable/Disable',
                description='Turn this rule on or off',
                value='toggle',
                emoji=self.emotes.onoff_icon,
                default=self.last_selected_action == 'toggle'
                if self.last_selected_action
                else False,
            ),
            discord.SelectOption(
                label='Delete Rule',
                description='⚠️ Permanently remove this rule',
                value='delete',
                emoji=self.emotes.deleteDanger_icon,
                default=self.last_selected_action == 'delete'
                if self.last_selected_action
                else False,
            ),
        ]

        if not self._action_select_added:
            self._create_action_select()
            self._action_select_added = True

        self.action_select.options = action_options
        self.action_select.disabled = False
        rule_name = next(r.name for r in self.rules if r.id == self.selected_rule_id)

        # Update placeholder based on last selected action
        if self.last_selected_action:
            action_labels = {
                'edit_actions': 'Edit Actions',
                'patterns': 'Patterns',
                'whitelist': 'Whitelist',
                'toggle': 'Toggle',
                'delete': 'Delete',
            }
            selected_label = action_labels.get(self.last_selected_action, self.last_selected_action)
            self.action_select.placeholder = (
                f'{self.emotes.gear_icon} {selected_label} for "{rule_name}"'
            )
        else:
            self.action_select.placeholder = f'⚙️ Configure "{rule_name}"...'

        # Refresh the entire view to update rule select defaults
        await self.load_data()
        embed = self._create_rule_detail_embed()
        await interaction.response.edit_message(embed=embed, view=self)

    def _create_action_select(self):
        self.action_select = discord.ui.Select(
            placeholder='⚙️ Choose an Action...',
            options=[discord.SelectOption(label='Select a rule first', value='none')],
            disabled=True,
            row=1,
        )
        self.action_select.callback = self._action_select_callback
        self.add_item(self.action_select)

    async def _action_select_callback(self, interaction: discord.Interaction['Bot']):
        if not await self.interaction_check(interaction):
            return

        if not self.selected_rule_id:
            return await interaction.response.send_message('Select a rule first.', ephemeral=True)

        action = self.action_select.values[0]
        self.last_selected_action = action  # Track selected action

        # Update the action select to show the current selection
        current_rule = next((r for r in self.rules if r.id == self.selected_rule_id), None)
        if current_rule:
            actions_text = ', '.join(a.value for a in (current_rule.actions or [])) or 'None'

            # Update action select options with current selection marked as default
            action_options = [
                discord.SelectOption(
                    label='Edit Actions',
                    description=f'Current: {actions_text[:50]}{"..." if len(actions_text) > 50 else ""}',
                    value='edit_actions',
                    emoji=self.emotes.gear_icon,
                    default=action == 'edit_actions',
                ),
                discord.SelectOption(
                    label='Patterns',
                    description=f'{len(self.patterns)} patterns configured',
                    value='patterns',
                    emoji=self.emotes.hash_icon,
                    default=action == 'patterns',
                ),
                discord.SelectOption(
                    label='Whitelist',
                    description=f'{len(self.whitelists)} safe words',
                    value='whitelist',
                    emoji=self.emotes.list_icon,
                    default=action == 'whitelist',
                ),
                discord.SelectOption(
                    label='Toggle Enable/Disable',
                    description='Turn this rule on or off',
                    value='toggle',
                    emoji=self.emotes.onoff_icon,
                    default=action == 'toggle',
                ),
                discord.SelectOption(
                    label='Delete Rule',
                    description='⚠️ Permanently remove this rule',
                    value='delete',
                    emoji=self.emotes.deleteDanger_icon,
                    default=action == 'delete',
                ),
            ]

            self.action_select.options = action_options

            # Update placeholder to show current selection
            action_labels = {
                'edit_actions': 'Edit Actions',
                'patterns': 'Patterns',
                'whitelist': 'Whitelist',
                'toggle': 'Toggle',
                'delete': 'Delete',
            }
            selected_label = action_labels.get(action, action)
            rule_name = current_rule.name
            self.action_select.placeholder = (
                f'{self.emotes.gear_icon} {selected_label} for "{rule_name}"'
            )

        if action == 'edit_actions':
            await self._edit_actions(interaction)
        elif action == 'patterns':
            await self._manage_patterns(interaction)
        elif action == 'whitelist':
            await self._manage_whitelist(interaction)
        elif action == 'toggle':
            await self._toggle_rule(interaction)
        elif action == 'delete':
            await self._delete_rule(interaction)

    @discord.ui.button(label='Add New Rule', emoji='➕', style=discord.ButtonStyle.green, row=2)
    async def add_rule_button(
        self, interaction: discord.Interaction['Bot'], button: discord.ui.Button
    ):
        if not await self.interaction_check(interaction):
            return

        name_input = discord.ui.TextInput(
            label='Rule Name',
            placeholder='e.g., Basic Profanity Filter',
            max_length=100,
            required=True,
        )

        modal = CustomModal(title='Create Anti-Swear Rule', options=[('name', name_input)])
        await interaction.response.send_modal(modal)

        if await modal.wait():
            return
        if not modal.interaction:
            return

        name = modal.saved_items['name'].value.strip()

        action_view = ProfanityActionSelectionView(
            self.bot,
            self.user,
            self.hub,
            self.permission,
            name,
            parent_view=self,
            locale=self.locale,
        )
        embed = action_view.create_embed()
        await modal.interaction.followup.send('Now select actions for this rule:', ephemeral=True)
        await interaction.edit_original_response(embed=embed, view=action_view)

    async def _rebuild_and_refresh(self, interaction: discord.Interaction):
        """Rebuild the view and refresh the display."""
        await interaction.response.defer()
        await self.load_data()
        embed = self.create_embed()
        await interaction.edit_original_response(embed=embed, view=self)

    async def _manage_patterns(self, interaction: discord.Interaction['Bot']):
        if not self.selected_rule_id:
            return await interaction.response.send_message('No rule selected.', ephemeral=True)

        current_patterns = [p.pattern for p in self.patterns]
        current_text = ', '.join(current_patterns) if current_patterns else ''

        patterns_input = discord.ui.TextInput(
            label='Word Patterns (comma-separated)',
            placeholder='bad, word, another',
            default=current_text,
            style=discord.TextStyle.paragraph,
            max_length=2000,
            required=False,
        )

        modal = CustomModal(title='Configure Patterns', options=[('patterns', patterns_input)])
        await interaction.response.send_modal(modal)

        if await modal.wait():
            return
        if not modal.interaction:
            return

        patterns_text = modal.saved_items['patterns'].value.strip()
        raw_patterns = (
            [p.strip() for p in patterns_text.split(',') if p.strip()] if patterns_text else []
        )

        # Helper to infer match type and clean pattern by asterisk rules
        def infer_pattern(
            p: str,
        ) -> tuple[Optional[str], Optional[PatternMatchType], Optional[str]]:
            # Normalize whitespace
            p = p.strip()
            if not p:
                return None, None, 'empty'

            star_count = p.count('*')
            # If too many asterisks (internal or repeated), mark invalid
            # Allowed forms: word, word*, *word, *word*
            if star_count == 0:
                return p, PatternMatchType.EXACT, None
            if star_count == 1:
                if p.endswith('*') and '*' not in p[:-1]:
                    # word*
                    base = p[:-1]
                    if not base:
                        return None, None, 'invalid: missing base before *'
                    return base, PatternMatchType.PREFIX, None
                if p.startswith('*') and '*' not in p[1:]:
                    # *word
                    base = p[1:]
                    if not base:
                        return None, None, 'invalid: missing base after *'
                    return base, PatternMatchType.SUFFIX, None
                return None, None, 'invalid: misplaced *'
            if star_count == 2:
                if p.startswith('*') and p.endswith('*') and '*' not in p[1:-1]:
                    # *word*
                    base = p[1:-1]
                    if not base:
                        return None, None, 'invalid: empty between * *'
                    return base, PatternMatchType.WILDCARD, None
                return None, None, 'invalid: misplaced **'
            # 3 or more stars not allowed
            return None, None, 'invalid: too many *'

        # Compute inferred patterns
        inferred: list[tuple[str, PatternMatchType]] = []
        invalid_inputs: list[tuple[str, str]] = []
        for rp in raw_patterns:
            base, mtype, err = infer_pattern(rp)
            if base and mtype:
                inferred.append((base, mtype))
            else:
                invalid_inputs.append((rp, err or 'invalid'))

        async with self.bot.db.get_session() as session:
            svc = ProfanityConfigService(session)

            # Clear existing patterns for rule
            for pattern in self.patterns:
                await svc.delete_pattern(hub_id=self.hub.id, pattern_id=pattern.id)

            # Add inferred patterns
            added = 0
            for base, mtype in inferred:
                ok = await svc.add_pattern(
                    hub_id=self.hub.id,
                    rule_id=self.selected_rule_id,
                    pattern=base,
                    match_type=mtype,
                )
                if ok:
                    added += 1

        # Reload patterns to update counts
        async with self.bot.db.get_session() as session:
            svc = ProfanityConfigService(session)
            self.patterns = await svc.list_patterns(self.selected_rule_id)

        # Update action select with new counts
        if hasattr(self, 'action_select'):
            # Find the patterns option and update its description
            for i, option in enumerate(self.action_select.options):
                if option.value == 'patterns':
                    self.action_select.options[
                        i
                    ].description = f'{len(self.patterns)} patterns configured'
                    break

        # Build feedback message
        msg = f'{self.emotes.tick} Updated patterns! ({added} added)'
        if invalid_inputs:
            # Show up to first 5 invalids
            preview = ', '.join(f'"{p}"' for p, _ in invalid_inputs[:5])
            extra = f' and {len(invalid_inputs) - 5} more' if len(invalid_inputs) > 5 else ''
            msg += f"\n{self.emotes.alert_icon} Skipped invalid inputs: {preview}{extra}. Use 'word', 'word*', '*word', or '*word*'."

        await modal.interaction.followup.send(msg, ephemeral=True)
        # Refresh the main embed with updated details
        embed = self._create_rule_detail_embed()
        await interaction.edit_original_response(embed=embed, view=self)

    async def _manage_whitelist(self, interaction: discord.Interaction['Bot']):
        if not self.selected_rule_id:
            return await interaction.response.send_message('No rule selected.', ephemeral=True)

        current_whitelist = [w.word for w in self.whitelists]
        current_text = ', '.join(current_whitelist) if current_whitelist else ''

        whitelist_input = discord.ui.TextInput(
            label='Safe Words (comma-separated)',
            placeholder='safe, word, another',
            default=current_text,
            style=discord.TextStyle.paragraph,
            max_length=2000,
            required=False,
        )

        modal = CustomModal(title='Configure Whitelist', options=[('whitelist', whitelist_input)])
        await interaction.response.send_modal(modal)

        if await modal.wait():
            return
        if not modal.interaction:
            return

        whitelist_text = modal.saved_items['whitelist'].value.strip()
        new_whitelist = (
            [w.strip() for w in whitelist_text.split(',') if w.strip()] if whitelist_text else []
        )

        async with self.bot.db.get_session() as session:
            svc = ProfanityConfigService(session)

            for whitelist_item in self.whitelists:
                await svc.delete_whitelist(
                    rule_id=self.selected_rule_id, whitelist_id=whitelist_item.id
                )

            for word in new_whitelist:
                await svc.add_whitelist(
                    rule_id=self.selected_rule_id, word=word, created_by=str(self.user.id)
                )

        # Reload whitelist to update counts
        async with self.bot.db.get_session() as session:
            svc = ProfanityConfigService(session)
            self.whitelists = await svc.list_whitelist(self.selected_rule_id)

        # Update action select with new counts
        if hasattr(self, 'action_select'):
            # Find the whitelist option and update its description
            for i, option in enumerate(self.action_select.options):
                if option.value == 'whitelist':
                    self.action_select.options[i].description = f'{len(self.whitelists)} safe words'
                    break

        await modal.interaction.followup.send(
            f'{self.emotes.tick} Updated whitelist! ({len(new_whitelist)} safe words)',
            ephemeral=True,
        )
        # Refresh the main embed with updated details
        embed = self._create_rule_detail_embed()
        await interaction.edit_original_response(embed=embed, view=self)

    async def _toggle_rule(self, interaction: discord.Interaction['Bot']):
        if not self.selected_rule_id:
            return await interaction.response.send_message('No rule selected.', ephemeral=True)

        async with self.bot.db.get_session() as session:
            svc = ProfanityConfigService(session)
            rule = await svc.get_rule(self.selected_rule_id)
            if not rule:
                return await interaction.response.send_message('Rule not found.', ephemeral=True)

            new_status = not rule.enabled
            success = await svc.update_rule(self.selected_rule_id, enabled=new_status)

        if success:
            status_text = 'enabled' if new_status else 'disabled'
            await interaction.response.send_message(
                f'{self.emotes.tick} Rule **{rule.name}** {status_text}!', ephemeral=True
            )
            self.last_selected_action = None  # Reset action selection
            await self.load_data()
            embed = self.create_embed()
            await interaction.edit_original_response(embed=embed, view=self)
        else:
            await interaction.response.send_message(
                f'{self.emotes.cross} Failed to update rule.', ephemeral=True
            )

    async def _delete_rule(self, interaction: discord.Interaction['Bot']):
        if not self.selected_rule_id:
            return await interaction.response.send_message('No rule selected.', ephemeral=True)

        async with self.bot.db.get_session() as session:
            svc = ProfanityConfigService(session)
            rule = await svc.get_rule(self.selected_rule_id)
            if not rule:
                return await interaction.response.send_message('Rule not found.', ephemeral=True)

            success = await svc.delete_rule(self.selected_rule_id)

        if success:
            await interaction.response.send_message(
                f'{self.emotes.trash_icon} Rule **{rule.name}** deleted!', ephemeral=True
            )
            self.selected_rule_id = None
            self.last_selected_action = None  # Reset action selection

            if self._action_select_added and hasattr(self, 'action_select'):
                self.remove_item(self.action_select)
                self._action_select_added = False

            await self.load_data()
            embed = self.create_embed()
            await interaction.edit_original_response(embed=embed, view=self)
        else:
            await interaction.response.send_message(
                f'{self.emotes.cross} Failed to delete rule.', ephemeral=True
            )

    async def _edit_actions(self, interaction: discord.Interaction['Bot']):
        """Edit the actions for the selected rule."""
        if not self.selected_rule_id:
            return await interaction.response.send_message('No rule selected.', ephemeral=True)

        # Get current rule data
        current_rule = next((r for r in self.rules if r.id == self.selected_rule_id), None)
        if not current_rule:
            return await interaction.response.send_message('Rule not found.', ephemeral=True)

        action_view = ProfanityActionSelectionView(
            self.bot,
            self.user,
            self.hub,
            self.permission,
            current_rule.name,
            parent_view=self,
            existing_rule=current_rule,
            locale=self.locale,
        )
        embed = action_view.create_embed()
        await interaction.response.edit_message(embed=embed, view=action_view)
