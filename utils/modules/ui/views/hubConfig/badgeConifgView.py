import discord
from discord import ui

from utils.modules.core.db.models import Hub
from utils.modules.ui.views.hubConfig.utils import BaseHubView
from utils.modules.ui.layouts.hubConfig.badgeConfigLayouts import BadgeEditModal
from utils.modules.hub.constants import <PERSON>bPermissionLevel

from typing import TYPE_CHECKING, Optional

if TYPE_CHECKING:
    from main import Bot
    from utils.modules.ui.views.hubConfig.hubConfigurationView import ConfigurationView


class BadgeConfigView(BaseHubView):
    def __init__(
        self,
        bot: 'Bot',
        user: discord.User | discord.Member,
        hub: Hub,
        permission: HubPermissionLevel,
        locale: str,
        parent_view: Optional['ConfigurationView'] = None,
    ):
        super().__init__(bot, user, hub, permission, locale)
        self.parent_view = parent_view

        self.add_back_button(self.parent_view)

    def load_select_options(self):
        options = [
            discord.SelectOption(
                emoji=self.bot.emotes.owner_badge,
                label='Owner Badge',
                description='This badge is displayed on te user who owns the hub.',
                value='owner',
            ),
            discord.SelectOption(
                emoji=self.bot.emotes.manager_badge,
                label='Manager Badge',
                description='This badge is displayed on te users who manage this hub.',
                value='manager',
            ),
            discord.SelectOption(
                emoji=self.bot.emotes.moderator_badge,
                label='Moderator Badge',
                description='This badge is displayed on te users who moderate this hub.',
                value='moderator',
            ),
        ]
        self.select_callback.options = options

    @ui.select(
        placeholder='Select a badge to configure',
        options=[discord.SelectOption(label='LOADING', value='NONE')],
        max_values=1,
        min_values=1,
    )
    async def select_callback(self, interaction: discord.Interaction, select: ui.Select):
        await interaction.response.send_modal(BadgeEditModal(self.bot, self.hub, select.values[0], self))
