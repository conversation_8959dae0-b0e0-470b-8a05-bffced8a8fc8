import discord
from discord import ui
from sqlalchemy import select
import json
from typing import TYPE_CHECKING

from utils.modules.core.db.models import Hub

if TYPE_CHECKING:
    from main import Bot


class BadgeEditModal(ui.Modal):
    enabled = ui.Label(
        text='Should this badge be visible?',
        component=ui.Select(
            placeholder='Enable or disable this badge',
            options=[
                discord.SelectOption(label='Yes', value='yes', description='Enable this badge.'),
                discord.SelectOption(label='No', value='no', description='Disable this badge.'),
            ],
            required=True,
        ),
    )

    def __init__(self, bot: 'Bot', hub: Hub, badge: str, parent_view: ui.View):
        super().__init__(title='Input Required')
        self.bot = bot
        self.hub = hub
        self.badge = badge
        self.parent_view = parent_view

    async def on_submit(self, interaction: discord.Interaction):
        try:
            await interaction.response.defer(ephemeral=True)

            async with self.bot.db.get_session() as session:
                stmt = select(Hub).where(Hub.id == self.hub.id)
                hub_obj = (await session.execute(stmt)).scalar_one_or_none()

                if not hub_obj:
                    await interaction.followup.send('Hub not found.', ephemeral=True)
                    return

                current_badges: dict[str, bool] = {}
                if hub_obj.customBadges:
                    try:
                        if isinstance(hub_obj.customBadges, str):
                            current_badges = json.loads(hub_obj.customBadges)
                        else:
                            current_badges = hub_obj.customBadges or {}
                    except (json.JSONDecodeError, TypeError):
                        current_badges = {}

                # It raises an AssertionError if the component is not a Select
                # But it should always be a Select as per our design
                # If you change it later, make sure to handle that case
                # Or I'll come find you
                assert isinstance(self.enabled.component, ui.Select)

                is_enabled = self.enabled.component.values[0] == 'yes'
                current_badges[self.badge] = is_enabled

                hub_obj.customBadges = current_badges
                await session.commit()

            embed = discord.Embed(
                title='Success!',
                description=f'{self.bot.emotes.tick} Updated {self.badge} badge!',
                color=discord.Color.green(),
            )
            await interaction.followup.send(embed=embed, ephemeral=True)
        except Exception:
            embed = discord.Embed(
                title='Error!',
                description=f'{self.bot.emotes.cross} An unexpected error occurred. Please try again.',
                color=discord.Color.red(),
            )
            await interaction.followup.send(embed=embed, ephemeral=True)

        new_embed = discord.Embed(
            title='Custom Badges',
            description='Configure custom badge visibility for your hub. At this time they have a set icon, and this can not be changed due to constraints.',
            color=self.bot.constants.color,
        )
        try:
            await interaction.edit_original_response(embed=new_embed, view=self.parent_view)
        except discord.NotFound:
            # This can happen if the original message was deleted.
            pass
