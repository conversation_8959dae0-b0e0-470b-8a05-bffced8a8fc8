import discord
from discord import ui

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from main import Bot

class AdditionalButtons(ui.ActionRow):
    def __init__(self, bot, user, locale):
        super().__init__()
        self.bot = bot
        self.user = user
        self.locale = locale

        self.add_item(ui.Button(emoji=bot.emotes.bot_icon, label='Support', style=discord.ButtonStyle.link, url='https://interchat.dev/'))
        self.add_item(ui.Button(emoji=bot.emotes.search_icon, label='Search', style=discord.ButtonStyle.grey))

class CommandsSelect(ui.ActionRow): # Todo: Fix me :)
    def __init__(self, bot, user, locale):
        super().__init__()
        self.bot = bot
        self.user = user
        self.locale = locale

        self.set_options()

    def set_options(self):
        seen = []
        options = []
        for category in self.bot.walk_commands():
            if category.extras.get('category') and category.extras.get('category') not in ('Group') and category.extras.get('category') not in seen:
                seen += (category.extras.get('category'))
                options.append(discord.SelectOption(label=category.extras.get('category'), description=f'View {category.extras.get("category")} commands', value=category.extras.get('category')))

        self.on_submit.options = options

    @ui.select(
        placeholder='Select a category',
        options=[
            discord.SelectOption(label='Loading...', description='None', value='NONE'),
        ],
        min_values=1,
        max_values=1
    )
    async def on_submit(self, interaction: discord.Interaction['Bot'], select: ui.Select):
        await interaction.response.send_message(f'Selected category: {select.values[0]}', ephemeral=True)

class HelpLayout(ui.LayoutView): # Todo: !! Revise Command categories !!
    def __init__(self, bot, user, locale):
        super().__init__(timeout=None)
        self.bot: Bot = bot
        self.user = user
        self.locale = locale

        container = ui.Container(
            ui.TextDisplay(f'##  {self.bot.emotes.question_icon} Explore InterChat Commands'),
            ui.Separator(spacing=discord.SeparatorSpacing.small),
            CommandsSelect(self.bot, self.user, self.locale),
            ui.Separator(spacing=discord.SeparatorSpacing.small),
            ui.TextDisplay(f':wave: Hey there! Welcome to InterChat - You’ve found our help menu. Above are buttons that direct you to various categories, which display the command and its function. You may also run the command. Need some more help? That’s alright; our support server is linked below. Drop by, we’re more than happy to help.'),
            ui.Separator(spacing=discord.SeparatorSpacing.small),
            AdditionalButtons(self.bot, self.user, self.locale),
            accent_colour=bot.constants.color
        )
        self.add_item(container)
