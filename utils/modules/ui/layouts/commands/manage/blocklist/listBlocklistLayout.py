from __future__ import annotations
from typing import TYPE_CHECKING, Literal
import discord
from discord import ui
from sqlalchemy import select, func
from sqlalchemy.orm import joinedload

from utils.modules.core.db.models import ServerBlocklist
from utils.modules.ui.layouts.commands.manage.blocklist.addBlockModal import AddBlockModal

if TYPE_CHECKING:
    from main import Bot

FilterType = Literal['all', 'users', 'servers']


class ListBlocklistView(ui.LayoutView):
    container = ui.Container()

    def __init__(
        self,
        bot: 'Bot',
        locale: str,
        server_id: str,
        current_page: int = 0,
        filter_type: FilterType = 'all',
        items_per_page: int = 10,
    ):
        super().__init__()
        self.bot = bot
        self.locale = locale
        self.server_id = server_id
        self.current_page = current_page
        self.filter_type: FilterType = filter_type
        self.items_per_page = items_per_page
        self.blocked_items = []
        self.total_items = 0
        self.total_pages = 0

        self.add_to_blocklist.emoji = self.bot.emotes.plus_icon

    async def load_and_update(self):
        self.blocked_items = await self._load_blocked_items(
            self.bot, self.server_id, self.current_page * self.items_per_page, self.items_per_page, self.filter_type
        )
        self.total_items = await self.get_total_items_count(self.bot, self.server_id, self.filter_type)
        self.total_pages = max(1, (self.total_items + self.items_per_page - 1) // self.items_per_page)
        self.update_container()

    async def _load_blocked_items(
        self, bot: Bot, server_id: str, offset: int = 0, limit: int = 10, filter_type: FilterType = 'all'
    ):
        async with bot.db.get_session() as session:
            stmt = (
                select(ServerBlocklist)
                .where(ServerBlocklist.serverId == server_id)
                .options(joinedload(ServerBlocklist.blockedServer), joinedload(ServerBlocklist.blockedUser))
            )

            if filter_type == 'users':
                stmt = stmt.where(ServerBlocklist.blockedUserId.is_not(None))
            elif filter_type == 'servers':
                stmt = stmt.where(ServerBlocklist.blockedServerId.is_not(None))

            stmt = stmt.limit(limit).offset(offset).order_by(ServerBlocklist.createdAt.desc())
            result = await session.execute(stmt)
            items = result.scalars().all()
            return items

    @staticmethod
    async def get_total_items_count(bot: Bot, server_id: str, filter_type: FilterType = 'all') -> int:
        async with bot.db.get_session() as session:
            stmt = select(func.count(ServerBlocklist.id)).where(ServerBlocklist.serverId == server_id)

            if filter_type == 'users':
                stmt = stmt.where(ServerBlocklist.blockedUserId.is_not(None))
            elif filter_type == 'servers':
                stmt = stmt.where(ServerBlocklist.blockedServerId.is_not(None))

            result = await session.execute(stmt)
            count = result.scalar() or 0
            return count

    def update_container(self):
        self.container.clear_items()
        # Filter buttons
        filter_row = ui.ActionRow()

        all_btn = ui.Button(
            label=f'All ({self.total_items})',
            style=discord.ButtonStyle.primary if self.filter_type == 'all' else discord.ButtonStyle.secondary,
            custom_id='filter_all',
        )
        user_btn = ui.Button(
            label='Users',
            style=discord.ButtonStyle.primary if self.filter_type == 'users' else discord.ButtonStyle.secondary,
            custom_id='filter_users',
        )
        server_btn = ui.Button(
            label='Servers',
            style=discord.ButtonStyle.primary if self.filter_type == 'servers' else discord.ButtonStyle.secondary,
            custom_id='filter_servers',
        )

        all_btn.callback = self.filter_all_callback
        user_btn.callback = self.filter_users_callback
        server_btn.callback = self.filter_servers_callback

        filter_row.add_item(all_btn)
        filter_row.add_item(user_btn)
        filter_row.add_item(server_btn)
        self.container.add_item(filter_row)

        self.container.add_item(ui.Separator())

        # Blocked items list
        if self.blocked_items:
            start_index = self.current_page * self.items_per_page + 1
            for i, item in enumerate(self.blocked_items):
                item_number = start_index + i

                person_icon = self.bot.emotes.person_icon
                house_icon = self.bot.emotes.house_icon
                delete_emoji = self.bot.emotes.delete

                display_text = (
                    f'### {item_number}. '
                    + (
                        f' {person_icon} {item.blockedUser.name if item.blockedUser else "Unknown User"}'
                        if item.blockedUserId is not None
                        else f' {house_icon} {item.blockedServer.name if item.blockedServer else "Unknown Server"}'
                    )
                    + f' (`{item.blockedUserId or item.blockedServerId}`)'
                    f'\n-# {item.reason or "No Reason"} • <t:{round(item.createdAt.timestamp())}:R>'
                )
                unblock_button = ui.Button(
                    emoji=delete_emoji, style=discord.ButtonStyle.danger, custom_id=f'unblock_{item.id}'
                )
                unblock_button.callback = lambda interaction, item_id=item.id: self.unblock_callback(
                    interaction, item_id
                )

                section = ui.Section(display_text, accessory=unblock_button)
                self.container.add_item(section)
        else:
            filter_text = {
                'all': "You're too nice! Block some servers/users to see them here.",
                'users': 'No blocked users found.',
                'servers': 'No blocked servers found.',
            }.get(self.filter_type, 'No items found.')

            title = f'### {self.bot.emotes.cross} {filter_text}\n'
            self.container.add_item(ui.TextDisplay(title))

            if self.filter_type == 'all':
                empty_message = (
                    'You can add servers or users to the blocklist using the "Add to Blocklist"\n'
                    'button in the previous message.'
                )
                self.container.add_item(ui.TextDisplay(empty_message))

        self.container.add_item(ui.Separator())
        if self.total_items > 0:
            pagination_info = f'-# Page {self.current_page + 1} of {self.total_pages} • {self.total_items} total items'
            self.container.add_item(ui.TextDisplay(pagination_info))

        navigation_row = ui.ActionRow()

        previous_emoji = self.bot.emotes.previous
        next_emoji = self.bot.emotes.next

        back_btn = ui.Button(
            emoji=previous_emoji,
            style=discord.ButtonStyle.secondary,
            disabled=self.current_page == 0,
            custom_id='page_back',
        )
        back_btn.callback = self.back_btn_callback

        next_btn = ui.Button(
            emoji=next_emoji,
            style=discord.ButtonStyle.secondary,
            disabled=self.current_page >= self.total_pages - 1,
            custom_id='page_next',
        )
        next_btn.callback = self.next_btn_callback

        navigation_row.add_item(back_btn)
        navigation_row.add_item(next_btn)
        self.container.add_item(navigation_row)

    async def filter_all_callback(self, interaction: discord.Interaction):
        self.filter_type = 'all'
        self.current_page = 0
        await self.load_and_update()
        await interaction.response.edit_message(view=self)

    async def filter_users_callback(self, interaction: discord.Interaction):
        self.filter_type = 'users'
        self.current_page = 0
        await self.load_and_update()
        await interaction.response.edit_message(view=self)

    async def filter_servers_callback(self, interaction: discord.Interaction):
        self.filter_type = 'servers'
        self.current_page = 0
        await self.load_and_update()
        await interaction.response.edit_message(view=self)

    async def unblock_callback(self, interaction: discord.Interaction, item_id: str):
        try:
            async with self.bot.db.get_session() as session:
                stmt = select(ServerBlocklist).where(ServerBlocklist.id == item_id)
                result = await session.execute(stmt)
                item_to_delete = result.scalar_one_or_none()

                if item_to_delete:
                    await session.delete(item_to_delete)
                    await session.commit()

                    await self.load_and_update()

                    if not self.blocked_items and self.current_page > 0:
                        self.current_page -= 1
                        await self.load_and_update()

                    await interaction.response.edit_message(view=self)
                else:
                    await interaction.response.send_message('❌ Item not found or already removed.', ephemeral=True)

        except Exception as e:
            await interaction.response.send_message(f'❌ Error removing item: {str(e)}', ephemeral=True)

    async def back_btn_callback(self, interaction: discord.Interaction):
        if self.current_page > 0:
            self.current_page -= 1
            await self.load_and_update()
            await interaction.response.edit_message(view=self)
        else:
            await interaction.response.send_message("You're already on the first page.", ephemeral=True)

    async def next_btn_callback(self, interaction: discord.Interaction):
        if self.current_page < self.total_pages - 1:
            self.current_page += 1
            await self.load_and_update()
            await interaction.response.edit_message(view=self)
        else:
            await interaction.response.send_message("You're already on the last page.", ephemeral=True)

    row = ui.ActionRow()

    @row.button(style=discord.ButtonStyle.primary)
    async def add_to_blocklist(self, interaction: discord.Interaction, button: ui.Button):
        await interaction.response.send_modal(AddBlockModal())
