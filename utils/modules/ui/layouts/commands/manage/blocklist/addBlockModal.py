from __future__ import annotations

from typing import TYPE_CHECKING
import discord

from utils.constants import logger
from utils.modules.core.db.models import ServerBlocklist

if TYPE_CHECKING:
    from main import Bot


class AddBlockModal(discord.ui.Modal, title='Add to Blocklist'):
    type = discord.ui.Label(
        text='Type',
        description='Specify whether you are blocking a User or a Server.',
        component=discord.ui.Select(
            placeholder='Select Type',
            options=[
                discord.SelectOption(label='User', value='user', description='Block a specific user.'),
                discord.SelectOption(label='Server', value='server', description='Block an entire server.'),
            ],
            min_values=1,
            max_values=1,
        ),
    )
    user_or_server_id = discord.ui.Label(
        text='User or Server ID to Block',
        component=discord.ui.TextInput(
            style=discord.TextStyle.short,
            placeholder='Enter the User ID or Server ID you wish to block',
            max_length=32,
            required=True,
        ),
    )

    reason = discord.ui.Label(
        text='Note (optional)',
        description="A small note helps you remember why you've blocked.",
        component=discord.ui.TextInput(
            style=discord.TextStyle.short,
            max_length=256,
            required=False,
            placeholder='E.g., Spammer, Annoying, Toxic etc.',
        ),
    )

    def __init__(self) -> None:
        super().__init__()

    async def on_submit(self, interaction: discord.Interaction['Bot']):  # pyright: ignore[reportIncompatibleMethodOverride]
        assert isinstance(self.user_or_server_id.component, discord.ui.TextInput)
        assert isinstance(self.reason.component, discord.ui.TextInput)
        assert isinstance(self.type.component, (discord.ui.Select))
        assert interaction.guild is not None, 'This modal must be used in a guild context.'

        user_or_server_id = self.user_or_server_id.component.value
        reason = self.reason.component.value or 'No Reason Provided'

        checks_passed = await self.run_checks(interaction, user_or_server_id)
        if not checks_passed:
            return

        type = self.type.component.values[0]
        created = await self.create_block_entry(interaction, user_or_server_id, type, reason)

        if not created:
            return

        await interaction.response.send_message(
            f'Blocked {user_or_server_id} with reason: {self.reason.component.value}',
            ephemeral=True,
        )

    async def run_checks(self, interaction: discord.Interaction['Bot'], user_or_server_id: str) -> bool:
        assert interaction.guild is not None, 'This modal must be used in a guild context.'
        cross = interaction.client.emotes.cross

        if (not user_or_server_id.isdigit()) or (len(user_or_server_id) < 17 or len(user_or_server_id) > 21):
            await interaction.response.send_message(
                f'{cross} Please provide a valid User ID or Server ID.', ephemeral=True
            )
            return False
        elif user_or_server_id == str(interaction.guild.id):
            await interaction.response.send_message(f'{cross} You cannot block this server itself.', ephemeral=True)
            return False
        elif user_or_server_id == str(interaction.user.id):
            await interaction.response.send_message(f'{cross} You cannot block yourself.', ephemeral=True)
            return False
        return True

    async def create_block_entry(
        self, interaction: discord.Interaction['Bot'], user_or_server_id: str, type: str, reason: str
    ) -> bool:
        assert interaction.guild is not None, 'This modal must be used in a guild context.'
        async with interaction.client.db.get_session() as session:
            blocked_entity = ServerBlocklist(
                serverId=str(interaction.guild.id),
                blockedUserId=user_or_server_id if type == 'user' else None,
                blockedServerId=user_or_server_id if type == 'server' else None,
                reason=reason,
            )
            try:
                session.add(blocked_entity)
                await session.commit()
            except Exception as e:
                await interaction.response.send_message(
                    f'Failed to block {user_or_server_id}. They might already be blocked.', ephemeral=True
                )
                logger.error(f'Error adding to blocklist: {e}', exc_info=e)
                return False
        return True

    async def on_error(self, interaction: discord.Interaction, error: Exception) -> None:
        await interaction.response.send_message('Oops! Something went wrong.', ephemeral=True)
        logger.error(f'Error in AddBlockModal: {error}', exc_info=error)
