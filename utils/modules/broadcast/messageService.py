import re
from typing import TYPE_CHECKING, Tuple, Optional

from utils.modules.broadcast.mediaProcessor import MediaProcessor
from utils.modules.broadcast.validation.utils.interfaces import ValidationResult
from utils.modules.core.db.models import Message
from utils.modules.core.db.models import User, Hub

import discord
from unidecode import unidecode

if TYPE_CHECKING:
    from main import Bot
    from utils.modules.services.userService import UserService


class FormattingService:
    _CONTENT_BLOCK_PATTERNS = [
        (re.compile(r'@(everyone|here)'), r'@\1'),
        (
            re.compile(r'(?:https?://)?discord(?:\.gg|app\.com/invite)/[a-zA-Z0-9-]+'),
            '[discord invite]',
        ),
    ]

    _NAME_BLOCK_PATTERNS = [
        (re.compile(r'discord', re.IGNORECASE), '****'),
        (re.compile(r'clyde', re.IGNORECASE), '****'),
        (re.compile(r'system', re.IGNORECASE), '****'),
    ]

    _WHITESPACE_PATTERN = re.compile(r'\s+')
    _INVALID_PATTERN = re.compile(r'^\s*$')

    @staticmethod
    def format_webhook_name(user: discord.User | discord.Member, guild: discord.Guild) -> str:
        """Sanitize and format a string suitable for a webhook username."""
        name = f'{unidecode(user.name)} | {unidecode(guild.name)}'

        for pattern, replacement in FormattingService._NAME_BLOCK_PATTERNS:
            name = pattern.sub(replacement, name)

        name = FormattingService._WHITESPACE_PATTERN.sub(' ', name).strip()

        if not name or FormattingService._INVALID_PATTERN.match(name):
            name = 'User'

        return name[:80]

    @staticmethod
    async def format_user_badges(
        bot: 'Bot', user: discord.User | discord.Member, usersvc: 'UserService', hub: Hub
    ) -> str:
        """Fetches and formats user badges into a single string."""
        show_badges, user_badges = await usersvc.fetch_badges(bot, str(user.id), True, False, hub)

        if not show_badges or not user_badges:
            return ''

        return ' '.join(badge['icon'] for badge in user_badges)

    @staticmethod
    def sanitize_content(content: str) -> str:
        """Sanitizes the main message content to remove unwanted pings and links."""
        for pattern, replacement in FormattingService._CONTENT_BLOCK_PATTERNS:
            content = pattern.sub(replacement, content)
        return content

    @staticmethod
    def format_reply(replied_to_user: discord.User | discord.Member, replied_to_content: str) -> str:
        """Formats a reply mention into a Discord blockquote format."""
        truncated_content = (replied_to_content[:75] + '...') if len(replied_to_content) > 75 else replied_to_content

        return f'> @{replied_to_user.name}: {truncated_content.replace(chr(10), " ")}\n'

    @staticmethod
    def format_attachments_and_stickers(message: discord.Message) -> str:
        """Formats attachments and stickers into a list of URLs."""
        lines = []
        for attachment in message.attachments:
            lines.append(attachment.url)
        for sticker in message.stickers:
            lines.append(sticker.url)

        return '\n'.join(lines)

    @staticmethod
    def assemble_final_content(
        content: str, badges: str = '', reply_mention: str = '', attachments_str: str = ''
    ) -> str:
        """Assembles all formatted parts into the final message string."""
        parts = []
        if badges:
            parts.append(f'-# {badges}')

        main_content = f'{reply_mention}{content}'

        if not content.strip() and attachments_str:
            parts.append(f'{reply_mention}{attachments_str}')
        else:
            parts.append(main_content)
            if attachments_str:
                parts.append(attachments_str)

        return '\n'.join(parts)


class MessageService:
    @staticmethod
    def create_message_embed(
        message: 'Message',
        author: 'User',
        max_content_length: int = 100,
        max_username_length: int = 30,
    ) -> discord.Embed:
        """Create an embed"""
        username = (author.name or 'Unknown User')[:max_username_length]

        if message.imageUrl:
            description = '*Message contains media*'
        elif not message.content:
            description = '*No content*'
        elif len(message.content) <= max_content_length:
            description = message.content
        else:
            description = message.content[:max_content_length] + '...'

        embed = discord.Embed(description=description)

        if hasattr(author, 'image') and author.image:
            embed.set_author(name=username, icon_url=author.image)

        return embed

    def create_reply_embed(
        self,
        message_reference: Optional[discord.MessageReference],
        reply_data: Optional[Tuple['Message', 'User', str]],
        target_channel_id: str,
        target_server_id: str,
        broadcast_message_id: Optional[str] = None,
    ) -> Optional[discord.Embed]:
        """Create reply embed if message is a reply."""
        if not (message_reference and message_reference.message_id and reply_data):
            return None

        original_message, author, _ = reply_data
        embed = self.create_message_embed(original_message, author)

        if broadcast_message_id:
            jump_link = self._build_discord_jump_link(
                target_server_id,
                target_channel_id,
                broadcast_message_id,
            )
            embed.description = f'[**Reply To**]({jump_link}): {embed.description or ""}'

        return embed

    @staticmethod
    def _build_discord_jump_link(guild_id: str, channel_id: str, message_id: str) -> str:
        """Build Discord jump link for message."""
        return f'https://discord.com/channels/{guild_id}/{channel_id}/{message_id}'

    # Message Storage Methods

    @staticmethod
    def create_message_object(
        message: discord.Message,
        hub: 'Hub',
        content: str,
        referred_message: Optional['Message'] = None,
    ) -> 'Message':
        """Create a Message object from Discord message"""
        assert message.guild is not None, 'Message must be from a guild'

        image_url = MediaProcessor.extract_image_url_from_content(content=content)
        guild_id = str(message.guild.id)
        referred_message_id = referred_message.id if referred_message else None

        return Message(
            id=str(message.id),
            hubId=hub.id,
            content=content,
            imageUrl=image_url,
            channelId=str(message.channel.id),
            guildId=guild_id,
            authorId=str(message.author.id),
            createdAt=message.created_at.replace(tzinfo=None),
            referredMessageId=referred_message_id,
        )

    # Message Validation Methods

    @staticmethod
    def validate_message_length(content: str, max_length: int = 2000) -> ValidationResult:
        """Validate message length"""
        return ValidationResult(
            is_valid=len(content or '') <= max_length,
            reason='Message Length',
            should_notify=True,
            notification_message='Your message length does not meet our constants.',
        )

    @staticmethod
    def should_process_message(message: discord.Message) -> bool:
        """Check if message should be processed"""
        return not (message.author.bot or message.author.system or not message.guild)
