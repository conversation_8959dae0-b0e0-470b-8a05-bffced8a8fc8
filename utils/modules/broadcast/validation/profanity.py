import discord
from sqlalchemy.ext.asyncio import AsyncSession  # Import AsyncSession for type hinting

from utils.constants import logger
from .utils.profanity.filterService import profanity_filter
from utils.modules.core.db.models import Hub

from .utils.interfaces import ValidationResult


async def validate_profanity(
    message: discord.Message, hub: Hub, session: AsyncSession
) -> ValidationResult:
    """
    Validates a message for profanity using a provided database session.
    """
    try:

        result = await profanity_filter.check_message(
            message=message.content,
            hub_id=hub.id,
            user_id=str(message.author.id),
            channel_id=str(message.channel.id),
            message_id=str(message.id),
            session=session,
        )
    except Exception as e:
        logger.error(f'ProfanityFilterService check failed for message {message.id}: {e}', exc_info=e)
        return ValidationResult(is_valid=True)

    if not result.blocked:
        return ValidationResult(is_valid=True)

    return ValidationResult(
        is_valid=False,
        reason='Profanity filter blocked message',
        should_notify=True,
        notification_message="🚫 Your message was blocked by the hub's profanity filter.",
    )
