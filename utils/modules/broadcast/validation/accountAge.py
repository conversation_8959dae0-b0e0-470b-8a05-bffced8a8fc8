import discord
from datetime import datetime, timedelta

from .utils.interfaces import ValidationResult


def validate_account_age(author: discord.User | discord.Member) -> ValidationResult:
    """Check if account meets minimum age requirement."""
    account_age = datetime.now(author.created_at.tzinfo) - author.created_at

    if not account_age > timedelta(days=7):
        return ValidationResult(
            is_valid=False,
            reason='Account too new',
            should_notify=True,
            notification_message=('Your account is too new to send messages through InterChat. Please wait a few more days and try again.'),
        )

    return ValidationResult(is_valid=True)
