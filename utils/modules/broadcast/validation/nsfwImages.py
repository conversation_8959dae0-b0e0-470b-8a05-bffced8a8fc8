import asyncio
import aiohttp
import discord
from utils.constants import logger, constants
from utils.modules.broadcast.mediaProcessor import MediaProcessor
from utils.modules.broadcast.validation.utils.interfaces import ValidationResult
from utils.modules.core.db.models import Hub
from utils.modules.core.hub.SettingsBitField import HubSettingsBit<PERSON>ield
from utils.modules.core.cache import cache_manager
from utils.modules.events.eventDispatcher import HubEventType, create_hub_event, event_dispatcher


async def validate_nsfw_images(message: discord.Message, hub: Hub) -> ValidationResult:
    """Detect NSFW content in static images via local detection service."""

    hub_settings = HubSettingsBitField(hub.settings)
    if (hub.nsfw and not hub_settings.has('BlockNSFW')) or not constants.enable_nsfw_detection:
        return ValidationResult(is_valid=True)

    try:
        urls = MediaProcessor.extract_static_image_urls(message)
        if not urls:
            return ValidationResult(is_valid=True)
    except Exception as e:
        logger.error(f'NSFW URL extraction failed: {e}', exc_info=True)
        return ValidationResult(is_valid=True)

    if not await _check_nsfw_rate_limit(message.author.id):
        return ValidationResult(is_valid=True)

    detected_urls = await _call_nsfw_detector(urls)
    if not detected_urls:
        return ValidationResult(is_valid=True)

    await _emit_nsfw_event(message, hub, detected_urls)
    logger.info(f'NSFW blocked: user {message.author.id}, hub {hub.id}')

    return ValidationResult(
        is_valid=False, should_notify=True, notification_message='🚫 Your message was blocked by InterChat filters (Reason: NSFW detection)'
    )


async def _check_nsfw_rate_limit(user_id: int) -> bool:
    """Check if user is within NSFW detection rate limits."""
    try:
        count = await cache_manager.increment('rate_limit', 'nsfw', str(user_id))
        if count is None:
            return True  # Cache failure, allow

        if count == 1:
            await cache_manager.expire('rate_limit', 'nsfw', str(user_id), ttl=60)

        if count > 5:
            logger.debug(f'NSFW rate limit exceeded for user {user_id}')
            return False
        return True
    except Exception as e:
        logger.warning(f'NSFW rate limit check failed: {e}')
        return True  # Allow on cache failure


async def _call_nsfw_detector(urls: list[str]) -> list[dict]:
    """Call NSFW detection service and return detected URLs with confidence >= 70%."""
    timeout = aiohttp.ClientTimeout(total=4)

    try:
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(constants.nsfw_detector_url, json={'urls': urls}) as resp:
                if resp.status != 200:
                    logger.warning(f'NSFW detector returned HTTP {resp.status}')
                    return []

                data = await resp.json()
                return [
                    {'url': item['url'], 'confidence_percentage': float(item.get('confidence_percentage', 0))}
                    for item in (data or [])
                    if isinstance(item, dict) and item.get('is_nsfw') and float(item.get('confidence_percentage', 0)) >= 70.0
                ]

    except (aiohttp.ClientError, asyncio.TimeoutError) as e:
        logger.warning(f'NSFW detector unavailable: {e}')
        return []
    except Exception as e:
        logger.error(f'NSFW detector error: {e}', exc_info=True)
        return []


async def _emit_nsfw_event(message: discord.Message, hub: Hub, detected_urls: list[dict]):
    """Emit NSFW detection event."""
    try:
        event = create_hub_event(
            event_type=HubEventType.NSFW_DETECTED,
            hub_id=hub.id,
            hub_name=hub.name,
            target_user_id=str(message.author.id),
            target_user_name=str(message.author.name),
            message_id=str(message.id),
            channel_id=str(message.channel.id),
            extra_data={'detected_urls': detected_urls, 'guild_id': str(message.guild.id) if message.guild else None},
        )
        await event_dispatcher.dispatch_hub_event(event)
    except Exception as e:
        logger.error(f'Failed to dispatch NSFW event: {e}', exc_info=True)
