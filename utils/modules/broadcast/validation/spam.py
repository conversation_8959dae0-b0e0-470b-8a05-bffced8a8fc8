from .utils.interfaces import ValidationResult
from utils.constants import logger
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from utils.modules.broadcast.spamService import SpamService


async def validate_spam(user_id: str, hub_id: str, spam_service: 'SpamService') -> ValidationResult:
    try:
        spam_result = await spam_service.check_spam(user_id, hub_id)

        if spam_result.is_spam:
            return ValidationResult(
                is_valid=False,
                reason='Spam detected',
                should_notify=True,
                notification_message=spam_result.warning_message,
                spam_action=spam_result.action_needed,
            )

        return ValidationResult(is_valid=True)

    except Exception as e:
        logger.warning(f'Spam validation failed for user {user_id} in hub {hub_id}: {e}')
        return ValidationResult(is_valid=True)
