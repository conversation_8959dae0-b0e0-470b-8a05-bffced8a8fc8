from enum import Enum
from typing import NamedTuple, Optional


class SpamAction(Enum):
    WARN = 'warn'
    MUTE = 'mute'


class ValidationResult(NamedTuple):
    is_valid: bool
    reason: Optional[str] = None
    should_notify: bool = False
    notification_message: Optional[str] = None
    infraction_id: Optional[str] = None
    spam_action: Optional[SpamAction] = None

    @classmethod
    def success(cls) -> 'ValidationResult':
        return cls(is_valid=True)

    @classmethod
    def failure(
        cls,
        reason: Optional[str] = None,
        should_notify: bool = False,
        notification_message: Optional[str] = None,
        infraction_id: Optional[str] = None,
        spam_action: Optional[SpamAction] = None,
    ) -> 'ValidationResult':
        return cls(
            is_valid=False,
            reason=reason,
            should_notify=should_notify,
            notification_message=notification_message,
            infraction_id=infraction_id,
            spam_action=spam_action,
        )


class ViolationType(Enum):
    USER_INFRACTION = 'user_infraction'
    SERVER_INFRACTION = 'server_infraction'
    USER_BLACKLIST = 'user_blacklist'
    SERVER_BLACKLIST = 'server_blacklist'
