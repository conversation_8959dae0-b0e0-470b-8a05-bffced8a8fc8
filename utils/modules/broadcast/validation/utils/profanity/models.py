
from __future__ import annotations
from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Optional, Set

from utils.modules.core.db.models import AlertSeverity, BlockWordAction, PatternMatchType


@dataclass
class MatchResult:
    """Result of a pattern match operation."""
    matched: bool
    pattern: str
    match_type: PatternMatchType
    matched_word: str
    start_pos: int
    end_pos: int
    pattern_id: Optional[str] = None


@dataclass
class BypassConfig:
    """Configuration for bypass checking."""
    exempt_roles: Set[str] = field(default_factory=set)
    exempt_moderators: bool = False
    exempt_managers: bool = False
    exempt_owners: bool = False


@dataclass
class RuleInfo:
    """A plain data snapshot of a rule to avoid ORM attribute access after its session closes."""
    id: str
    name: str
    enabled: bool
    actions: List[BlockWordAction]
    muteDurationMinutes: Optional[int]


@dataclass
class ActionContext:
    """Contextual information needed to execute an action."""
    hub_id: str
    hub_name: str
    user_id: str
    user_name: str
    channel_id: str
    message_id: Optional[str]
    triggered_word: str
    rule: RuleInfo

    @property
    def infraction_reason(self) -> str:
        """Standardized reason string for infractions."""
        return f"Profanity filter violation: '{self.triggered_word}' (Rule: {self.rule.name})"


@dataclass
class FilterResult:
    """Result of profanity filter check."""
    blocked: bool
    matches: List[MatchResult]
    actions_executed: List[BlockWordAction]
    bypass_reason: Optional[str] = None
    severity: AlertSeverity = AlertSeverity.LOW
    rule_id: Optional[str] = None

    @classmethod
    def clean(cls, matches: Optional[List[MatchResult]] = None) -> FilterResult:
        """Creates a FilterResult for a clean (non-blocked) message."""
        return cls(
            blocked=False,
            matches=matches or [],
            actions_executed=[]
        )

    @classmethod
    def bypassed(cls, reason: str) -> FilterResult:
        """Creates a FilterResult for a message that was bypassed."""
        return cls(
            blocked=False,
            matches=[],
            actions_executed=[],
            bypass_reason=reason
        )


@dataclass
class CachedRuleData:
    """A lightweight container for cached rule metadata."""
    loaded_at: datetime
