import re
import unicodedata
from typing import Dict, List, Optional, Set, Tuple

from utils.modules.core.db.models import PatternMatchType

from .models import MatchResult


class PatternMatcher:
    def __init__(self):
        self._raw_patterns: Dict[PatternMatchType, Dict[str, str]] = {t: {} for t in PatternMatchType}
        self._compiled_patterns: Dict[PatternMatchType, Optional[re.Pattern]] = {t: None for t in PatternMatchType}
        self._is_dirty: Dict[PatternMatchType, bool] = {t: False for t in PatternMatchType}
        self._whitelist: Set[str] = set()
        _substitutions = {
            ord('@'): 'a',
            ord('4'): 'a',
            ord('3'): 'e',
            ord('1'): 'i',
            ord('!'): 'i',
            ord('0'): 'o',
            ord('5'): 's',
            ord('7'): 't',
            ord('+'): 't',
            ord('$'): 's',
            ord('|'): 'l',
            ord('*'): None,
            ord('#'): 'h',
        }
        self._translation_table = str.maketrans(_substitutions)
        self._group_name_to_pattern_info: Dict[str, Tuple[str, str]] = {}

    def add_pattern(self, pattern: str, match_type: PatternMatchType, pattern_id: Optional[str] = None) -> bool:
        if not pattern or not pattern.strip():
            return False
        self._raw_patterns[match_type][pattern] = pattern_id or ''
        self._is_dirty[match_type] = True
        return True

    def add_whitelist_word(self, word: str) -> bool:
        if not word or not word.strip():
            return False
        normalized_word = self._normalize_text(word.strip())
        self._whitelist.add(normalized_word)
        return True

    def check_message(self, message: str) -> List[MatchResult]:
        if not message or not message.strip():
            return []
        normalized_message = self._normalize_text(message)
        message_words = self._extract_words(normalized_message)
        matches = []
        for match_type in PatternMatchType:
            if self._is_dirty[match_type]:
                self._compile_all_patterns(match_type)
            compiled_regex = self._compiled_patterns[match_type]
            if not compiled_regex:
                continue
            for match in compiled_regex.finditer(normalized_message):
                matched_word = match.group(0)
                if self._is_whitelisted(matched_word, message_words):
                    continue
                pattern_key = match.lastgroup
                if pattern_key:
                    original_pattern, pattern_id = self._group_name_to_pattern_info[pattern_key]
                    result = MatchResult(
                        matched=True,
                        pattern=original_pattern,
                        match_type=match_type,
                        matched_word=matched_word,
                        start_pos=match.start(),
                        end_pos=match.end(),
                        pattern_id=pattern_id,
                    )
                    matches.append(result)
        return matches

    def _compile_all_patterns(self, match_type: PatternMatchType) -> None:
        patterns_to_compile = self._raw_patterns[match_type]
        if not patterns_to_compile:
            self._compiled_patterns[match_type] = None
            self._is_dirty[match_type] = False
            return
        regex_parts = []
        temp_group_map = {}
        for i, (original_pattern, pattern_id) in enumerate(patterns_to_compile.items()):
            group_name = f'p{i}_{match_type.value}'
            normalized = self._normalize_text(original_pattern.strip())
            escaped = re.escape(normalized)
            regex_parts.append(f'(?P<{group_name}>{escaped})')
            temp_group_map[group_name] = (original_pattern, pattern_id)

        self._group_name_to_pattern_info.update(temp_group_map)
        combined_inner_pattern = '|'.join(regex_parts)

        scaffolding = {
            PatternMatchType.EXACT: r'\b(?:{})\b',
            PatternMatchType.PREFIX: r'\b(?:{}\w*)\b',
            PatternMatchType.SUFFIX: r'\b(?:\w*{})\b',
            PatternMatchType.WILDCARD: r'\b(?:\w*{}\w*)\b',
        }

        final_regex_str = scaffolding[match_type].format(combined_inner_pattern)

        try:
            self._compiled_patterns[match_type] = re.compile(final_regex_str, re.IGNORECASE | re.UNICODE)
        except re.error:
            self._compiled_patterns[match_type] = None
        self._is_dirty[match_type] = False

    def _normalize_text(self, text: str) -> str:
        text = text.casefold()
        text = unicodedata.normalize('NFD', text)
        return text.translate(self._translation_table)

    @staticmethod
    def _extract_words(text: str) -> Set[str]:
        return set(re.findall(r'\b\w+\b', text))

    def _is_whitelisted(self, matched_word: str, message_words: Set[str]) -> bool:
        if matched_word in self._whitelist:
            return True
        return any(word in self._whitelist and matched_word in word for word in message_words)
