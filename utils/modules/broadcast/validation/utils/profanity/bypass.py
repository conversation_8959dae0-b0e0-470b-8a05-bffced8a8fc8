# This comment made me laugh... if ykyk
from dataclasses import dataclass
from typing import  List, Optional

from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession

from utils.modules.core.db.models import Hub, HubModerator, HubModeratorRole

from .models import BypassConfig

@dataclass
class _UserPermissions:
    is_owner: bool = False
    moderator_role: Optional[HubModeratorRole] = None


class BypassChecker:
    async def _get_user_permissions(
        self, hub_id: str, user_id: str, session: AsyncSession
    ) -> _UserPermissions:
        """
        Fetches a user's owner and moderator status for a hub using a single,
        provided database session.
        """
        owner_query = select(Hub.ownerId).where(Hub.id == hub_id)
        mod_query = select(HubModerator.role).where(
            and_(HubModerator.hubId == hub_id, HubModerator.userId == user_id)
        )

        owner_id_result = await session.execute(owner_query)
        owner_id = owner_id_result.scalar_one_or_none()

        mod_role_result = await session.execute(mod_query)
        mod_role = mod_role_result.scalar_one_or_none()

        is_owner = (owner_id is not None and str(owner_id) == user_id)
        return _UserPermissions(is_owner=is_owner, moderator_role=mod_role)

    async def check_bypass(
        self,
        hub_id: str,
        user_id: str,
        session: AsyncSession,  # <-- Session is passed in here
        user_roles: Optional[List[str]] = None,
        bypass_config: Optional[BypassConfig] = None,
    ) -> Optional[str]:
        """
        Checks if a user's message should bypass the profanity filter based on their
        role within the hub, using a provided database session.
        """
        cfg = bypass_config or BypassConfig()

        permissions = await self._get_user_permissions(hub_id, user_id, session)

        if cfg.exempt_owners and permissions.is_owner:
            return 'hub_owner'
        if permissions.moderator_role:
            if cfg.exempt_managers and permissions.moderator_role == HubModeratorRole.MANAGER:
                return 'hub_manager'
            if cfg.exempt_moderators and permissions.moderator_role == HubModeratorRole.MODERATOR:
                return 'hub_moderator'
        if cfg.exempt_roles and user_roles and any(role in cfg.exempt_roles for role in user_roles):
            return 'exempt_role'
        return None
