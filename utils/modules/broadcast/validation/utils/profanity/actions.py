import asyncio
from typing import Any, Callable, Dict, List, Optional

from sqlalchemy.ext.asyncio import AsyncSession

from utils.constants import logger
from utils.modules.core.db.models import BlockWordAction, InfractionType
from utils.modules.events.eventDispatcher import Hub<PERSON>ventType, create_hub_event, event_dispatcher
from utils.modules.moderation.services import ModerationService

from .models import ActionContext


class ActionExecutor:
    def __init__(self, client_id: str):
        self.client_id = client_id
        self._handlers: Dict[BlockWordAction, Callable[..., Any]] = {
            BlockWordAction.BLOCK_MESSAGE: self._block_message,
            BlockWordAction.WARN: self._warn_user,
            BlockWordAction.MUTE: self._mute_user,
            BlockWordAction.BAN: self._ban_user,
            BlockWordAction.BLACKLIST: self._ban_user,
            BlockWordAction.SEND_ALERT: self._send_alert,
        }

    async def execute_actions(
        self, actions: List[BlockWordAction], context: ActionContext, session: AsyncSession
    ) -> List[BlockWordAction]:
        """
        Executes a list of actions using the provided session.
        """
        if not actions:
            return []

        tasks = [self._execute_single_action(action, context, session) for action in actions]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        executed_actions = []
        for action, result in zip(actions, results):
            if isinstance(result, Exception):
                logger.error(
                    f'Error executing action {action} for user {context.user_id}: {result}',
                    exc_info=result,
                )
            elif result is True:
                executed_actions.append(action)
        return executed_actions

    async def _execute_single_action(
        self, action: BlockWordAction, context: ActionContext, session: AsyncSession
    ) -> bool:
        handler = self._handlers.get(action)
        if not handler:
            logger.warning(f'No handler defined for action: {action}')
            return False

        await handler(context, session=session)
        return True

    async def _block_message(self, context: ActionContext, **kwargs) -> None:
        pass

    async def _warn_user(self, context: ActionContext, session: AsyncSession) -> None:
        await self._create_infraction(context, InfractionType.WARNING, session)

    async def _mute_user(self, context: ActionContext, session: AsyncSession) -> None:
        duration_minutes = context.rule.muteDurationMinutes or 60
        await self._create_infraction(context, InfractionType.MUTE, session, duration_ms=duration_minutes * 60 * 1000)

    async def _ban_user(self, context: ActionContext, session: AsyncSession) -> None:
        await self._create_infraction(context, InfractionType.BAN, session)

    @staticmethod
    async def _send_alert(context: ActionContext, **kwargs) -> None:
        event = create_hub_event(
            event_type=HubEventType.PROFANITY_VIOLATION,
            hub_id=context.hub_id,
            hub_name=context.hub_name,
            target_user_id=context.user_id,
            target_user_name=context.user_name,
            message_id=context.message_id,
            channel_id=context.channel_id,
            extra_data={'triggered_word': context.triggered_word, 'rule_name': context.rule.name},
        )
        await event_dispatcher.dispatch_hub_event(event)

    async def _create_infraction(
        self,
        context: ActionContext,
        infraction_type: InfractionType,
        session: AsyncSession,
        duration_ms: Optional[int] = None,
    ) -> None:
        modsvc = ModerationService(session)
        await modsvc.create_infraction(
            hub_id=context.hub_id,
            user_id=context.user_id,
            mod_id=self.client_id,
            infraction_type=infraction_type,
            reason=context.infraction_reason,
            duration_ms=duration_ms,
        )
