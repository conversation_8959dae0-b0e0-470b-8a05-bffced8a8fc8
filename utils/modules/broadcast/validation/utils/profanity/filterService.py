import asyncio
from collections import defaultdict
from datetime import datetime, timedelta
from typing import Dict, <PERSON>, <PERSON>tional, Tuple

from sqlalchemy import and_, select
from sqlalchemy.orm import joinedload
from sqlalchemy.ext.asyncio import AsyncSession

from utils.constants import logger
from utils.modules.core.db.models import AlertSeverity, AntiSwearRule, BlockWordAction
from utils.modules.events.eventDispatcher import Hub<PERSON><PERSON><PERSON>ype, create_hub_event, event_dispatcher

from .actions import ActionExecutor
from .bypass import BypassChecker
from .patternMatcher import PatternMatcher
from .models import ActionContext, CachedRuleData, FilterResult, MatchResult, RuleInfo
from utils.constants import constants

class ProfanityFilterService:
    def __init__(self, client_id: str = str(constants.client_id)):
        self.client_id = client_id
        self.action_executor = ActionExecutor(client_id)
        self.bypass_checker = BypassChecker()
        self._hub_cache: Dict[str, CachedRuleData] = {}
        self._matchers: Dict[str, PatternMatcher] = {}
        self._pattern_to_rule_cache: Dict[str, Dict[str, RuleInfo]] = {}
        self._cache_ttl = timedelta(minutes=15)
        self._hub_locks: Dict[str, asyncio.Lock] = defaultdict(asyncio.Lock)

    async def check_message(
        self,
        message: str,
        hub_id: str,
        user_id: str,
        channel_id: str,
        session: AsyncSession,
        message_id: Optional[str] = None,
    ) -> FilterResult:
        if not message or not message.strip():
            return FilterResult.clean()

        matcher = await self._ensure_rules_loaded(hub_id, session)
        if not matcher:
            return FilterResult.clean()

        if bypass_reason := await self.bypass_checker.check_bypass(hub_id, user_id, session):
            return FilterResult.bypassed(bypass_reason)

        matches = matcher.check_message(message)
        if not matches:
            return FilterResult.clean()

        primary_match, rule = self._find_primary_match_cached(matches, hub_id)
        if not (rule and rule.enabled and primary_match):
            return FilterResult.clean(matches=matches)

        severity = self._calculate_severity(rule)
        context = ActionContext(
            hub_id=hub_id, user_id=user_id, channel_id=channel_id, message_id=message_id,
            triggered_word=primary_match.matched_word, rule=rule, hub_name='N/A', user_name='N/A'
        )

        actions_executed = await self.action_executor.execute_actions(rule.actions, context, session)

        if actions_executed:
            await self._log_violation(message, primary_match, rule, severity, context)

        return FilterResult(
            blocked=BlockWordAction.BLOCK_MESSAGE in actions_executed,
            matches=matches, actions_executed=actions_executed, severity=severity, rule_id=rule.id,
        )

    async def reload_rules(self, hub_id: str, session: AsyncSession) -> None:
        async with self._hub_locks[hub_id]:
            self._hub_cache.pop(hub_id, None)
            self._matchers.pop(hub_id, None)
            self._pattern_to_rule_cache.pop(hub_id, None)
            await self._load_hub_rules(hub_id, session)

    async def _ensure_rules_loaded(self, hub_id: str, session: AsyncSession) -> Optional[
        PatternMatcher]:
        cache = self._hub_cache.get(hub_id)
        if not cache or (datetime.now() - cache.loaded_at) > self._cache_ttl:
            await self.reload_rules(hub_id, session)
        return self._matchers.get(hub_id)

    async def _load_hub_rules(self, hub_id: str, session: AsyncSession) -> None:
        query = (
            select(AntiSwearRule)
            .options(joinedload(AntiSwearRule.patterns), joinedload(AntiSwearRule.whitelists))
            .where(and_(AntiSwearRule.hubId == hub_id, AntiSwearRule.enabled.is_(True)))
        )
        rules_from_db = (await session.execute(query)).scalars().unique().all()

        matcher = PatternMatcher()
        pattern_to_rule_map: Dict[str, RuleInfo] = {}
        for rule in rules_from_db:
            rule_info = RuleInfo(
                id=rule.id, name=rule.name, enabled=bool(rule.enabled),
                actions=list(rule.actions or []), muteDurationMinutes=rule.muteDurationMinutes,
            )
            for pattern in rule.patterns:
                if matcher.add_pattern(pattern.pattern, pattern.matchType, pattern.id):
                    pattern_to_rule_map[pattern.id] = rule_info
            for whitelist_item in rule.whitelists:
                matcher.add_whitelist_word(whitelist_item.word)

        self._matchers[hub_id] = matcher
        self._pattern_to_rule_cache[hub_id] = pattern_to_rule_map
        self._hub_cache[hub_id] = CachedRuleData(loaded_at=datetime.now())

    def _find_primary_match_cached(self, matches: List[MatchResult], hub_id: str) -> Tuple[
        Optional[MatchResult], Optional[RuleInfo]]:
        rule_map = self._pattern_to_rule_cache.get(hub_id, {})
        best_match, best_rule, best_priority = None, None, -1
        for match in matches:
            if match.pattern_id and (rule := rule_map.get(match.pattern_id)):
                priority = self._calculate_rule_priority(rule)
                if priority > best_priority:
                    best_match, best_rule, best_priority = match, rule, priority
        return best_match, best_rule

    @staticmethod
    def _calculate_rule_priority(rule: RuleInfo) -> int:
        action_weights = {
            BlockWordAction.BLOCK_MESSAGE: 1, BlockWordAction.WARN: 2,
            BlockWordAction.SEND_ALERT: 3,
            BlockWordAction.MUTE: 4, BlockWordAction.BAN: 5, BlockWordAction.BLACKLIST: 5,
        }
        return sum(action_weights.get(action, 0) for action in rule.actions)

    @staticmethod
    def _calculate_severity(rule: RuleInfo) -> AlertSeverity:
        actions = set(rule.actions)
        if BlockWordAction.BAN in actions or BlockWordAction.BLACKLIST in actions:
            return AlertSeverity.CRITICAL
        if BlockWordAction.MUTE in actions:
            return AlertSeverity.HIGH
        if BlockWordAction.WARN in actions:
            return AlertSeverity.MEDIUM
        return AlertSeverity.LOW

    @staticmethod
    async def _log_violation(
        message: str, match: MatchResult, rule: RuleInfo, severity: AlertSeverity,
        context: ActionContext
    ) -> None:
        try:
            event = create_hub_event(
                event_type=HubEventType.PROFANITY_VIOLATION, hub_id=context.hub_id,
                hub_name=context.hub_name,
                target_user_id=context.user_id, target_user_name=context.user_name,
                message_id=context.message_id,
                channel_id=context.channel_id, original_content=message,
                extra_data={
                    'triggered_word': match.matched_word, 'rule_id': rule.id,
                    'severity': severity.value,
                },
            )
            await event_dispatcher.dispatch_hub_event(event)
        except Exception as e:
            logger.error(f'Failed to log profanity violation event: {e}', exc_info=True)



profanity_filter = ProfanityFilterService()
