from datetime import datetime

from sqlalchemy import and_, or_, select, union_all
from sqlalchemy.ext.asyncio import AsyncSession

from utils.modules.broadcast.validation.utils.interfaces import ValidationResult
from utils.modules.core.db.models import (
    Blacklist,
    Infraction,
    InfractionStatus,
    InfractionType,
    ServerBlacklist,
)


BANNABLE_INFRACTIONS = [InfractionType.BAN, InfractionType.MUTE, InfractionType.BLACKLIST]


async def validate_access_restrictions(
    user_id: str, guild_id: str, hub_id: str, session: AsyncSession
) -> ValidationResult:
    now = datetime.now()

    user_infraction_query = select(Infraction.reason).where(
        and_(
            Infraction.hubId == hub_id,
            Infraction.userId == user_id,
            Infraction.status == InfractionStatus.ACTIVE,
            Infraction.type.in_(BANNABLE_INFRACTIONS),
            or_(Infraction.expiresAt.is_(None), Infraction.expiresAt > now),
        )
    )

    server_infraction_query = select(Infraction.reason).where(
        and_(
            Infraction.hubId == hub_id,
            Infraction.serverId == guild_id,
            Infraction.status == InfractionStatus.ACTIVE,
            Infraction.type.in_(BANNABLE_INFRACTIONS),
            or_(Infraction.expiresAt.is_(None), Infraction.expiresAt > now),
        )
    )

    user_blacklist_query = select(Blacklist.reason).where(
        and_(
            Blacklist.userId == user_id,
            or_(Blacklist.expiresAt.is_(None), Blacklist.expiresAt > now),
        )
    )

    server_blacklist_query = select(ServerBlacklist.reason).where(
        and_(
            ServerBlacklist.serverId == guild_id,
            or_(ServerBlacklist.expiresAt.is_(None), ServerBlacklist.expiresAt > now),
        )
    )

    combined_query = union_all(
        user_infraction_query, server_infraction_query, user_blacklist_query, server_blacklist_query
    ).limit(1)

    result = (await session.execute(combined_query)).first()

    if result:
        return ValidationResult.failure(f'Access restricted: {result.reason}')

    return ValidationResult(is_valid=True)
