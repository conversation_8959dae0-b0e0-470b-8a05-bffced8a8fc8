import asyncio
import dataclasses
from typing import TYPE_CHECKING, List, Optional, Sequence, Tuple

import discord
from sqlalchemy import and_, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from utils.constants import logger, redis_client
from utils.modules.broadcast.notify import NotificationManager
from utils.modules.broadcast.mediaProcessor import MediaProcessor
from utils.modules.broadcast.spamService import SpamService
from utils.modules.broadcast.utils.broadcast.exponential import ExponentialBackoff
from utils.modules.broadcast.utils.messageUtils import (
    get_broadcast_ids_for_channels,
    is_reply_mention_required,
    store_message_and_broadcasts,
)
from utils.modules.broadcast.validation.blockLists import check_if_blocked
from utils.modules.broadcast.validation.utils.interfaces import SpamAction, ValidationResult
from utils.modules.broadcast.validationService import ValidationService
from utils.modules.common.user_utils import UserManager
from utils.modules.core.db.models import Connection, Hub, HubRulesAcceptance, Message, ServerData, User
from utils.modules.core.moderation import fetch_original_msg_with_extra
from utils.modules.services.userService import UserService
from utils.modules.ui.views.hubRulesAcceptanceView import HubRulesAcceptanceView
from utils.utils import filter_user_mentions

from .messageService import FormattingService, MessageService
from .utils.models import BroadcastPayload, BroadcastPrerequisites, HubContext, ReplyContext

if TYPE_CHECKING:
    from main import Bot

SentMessageInfo = Tuple[str, str, str]  # (message_id, channel_id, server_id)


class BroadcastService:
    def __init__(self, bot: 'Bot'):
        self.bot = bot
        self.spam_svc = SpamService()
        self.validation_svc = ValidationService(self.spam_svc)
        self.formatting_svc = FormattingService()
        self.message_svc = MessageService()
        self.media_processor = MediaProcessor()

    async def process_and_broadcast_message(self, message: discord.Message) -> None:
        if not (message.guild and isinstance(message.author, discord.Member)):
            return

        async with self.bot.db.get_session() as session:
            context = await self._get_hub_context(session, message.channel.id, message.author)
            if not context:
                return

            await UserService(session).upsert_user(message.author, commit=False)

            if self._user_must_accept_rules(context.hub, context.rules_acceptance):
                await self._send_welcome_rules(message, context.hub)
                return

            validation_result = await self.validation_svc.validate_message(
                message, context.hub, message.author, message.guild, session
            )
            if not validation_result.is_valid:
                await self._handle_validation_failure(session, message, validation_result, context.hub)
                await session.commit()
                return

            await self._perform_broadcast(session, message, context)
            await session.commit()

    async def _perform_broadcast(self, session: AsyncSession, message: discord.Message, context: HubContext) -> None:
        prereqs = await self._gather_broadcast_prerequisites(
            session, message, context.hub, context.connection.channelId
        )
        if not prereqs.other_connections:
            logger.info(f'No other connections found for Hub {context.hub.id}, skipping broadcast.')
            return

        payload = self._prepare_payload(message, prereqs.badge_prefix, prereqs.reply_data)

        sent_messages = await self._dispatch_broadcasts(message, prereqs, payload)

        await store_message_and_broadcasts(
            message,
            context.hub,
            message.content,
            sent_messages,
            session,
            referred_message=prereqs.reply_data.message if prereqs.reply_data else None,
        )

        await self._update_analytics_counts(session, message)

        assert message.guild is not None
        redis_key = f'interchat:hub:{context.hub.id}:last_context'
        current_context = f'{message.author.id}:{message.guild.id}'
        await redis_client.set(redis_key, current_context, ex=86400)

    async def _handle_validation_failure(
        self, session: AsyncSession, message: discord.Message, result: ValidationResult, hub: Hub
    ) -> None:
        infraction_id = None
        if result.spam_action == SpamAction.MUTE:
            infraction_id = await self.spam_svc.mute_user_for_spam(self.bot, message.author, hub, session)
        elif result.spam_action == SpamAction.WARN:
            infraction_id = await self.spam_svc.warn_user_for_spam(self.bot, message.author, hub, session)

        if result.should_notify and result.notification_message:
            notifier = NotificationManager(session)
            await notifier.send_validation_notification(message.author, result.notification_message, infraction_id)

    async def _gather_broadcast_prerequisites(
        self, session: AsyncSession, message: discord.Message, hub: Hub, source_channel_id: str
    ) -> BroadcastPrerequisites:
        other_connections = await self._get_other_connections(session, hub.id, source_channel_id)
        reply_data = await self._fetch_reply_data(session, message)
        badge_prefix = await self._prepare_badge_prefix(session, message, hub)

        reply_context = None
        channel_to_broadcast_id = {}
        if reply_data:
            original_message, original_user, _ = reply_data
            reply_context = ReplyContext(message=original_message, user=original_user, hub_id=hub.id)
            channel_ids = [c.channelId for c in other_connections]
            channel_to_broadcast_id = await get_broadcast_ids_for_channels(session, original_message.id, channel_ids)

        return BroadcastPrerequisites(other_connections, badge_prefix, reply_context, channel_to_broadcast_id)

    def _prepare_payload(
        self, message: discord.Message, badge_prefix: str, reply_data: Optional[ReplyContext]
    ) -> BroadcastPayload:
        assert message.guild is not None, 'Message must be from a guild'

        content, attachment_urls = self.media_processor.process_media(
            message.content, message.attachments, message.stickers
        )
        webhook_name = self.formatting_svc.format_webhook_name(message.author, message.guild)

        return BroadcastPayload(
            content=content,
            author_name=webhook_name,
            author_avatar_url=message.author.display_avatar.url,
            badge_prefix=badge_prefix,
            reply_data=reply_data,
            attachment_urls=attachment_urls,
        )

    async def _prepare_badge_prefix(self, session: AsyncSession, message: discord.Message, hub: Hub) -> str:
        redis_key = f'interchat:hub:{hub.id}:last_context'
        last_context_from_redis = await redis_client.get(redis_key)

        last_context = last_context_from_redis if last_context_from_redis else None

        last_author_id = last_context.split(':')[0] if last_context else None
        current_author_id = str(message.author.id)

        if last_author_id != current_author_id:
            user_svc = UserService(session)
            return await self.formatting_svc.format_user_badges(self.bot, message.author, user_svc, hub)
        return ''

    @staticmethod
    def _format_final_content(
        content: str, badge_prefix: str, target_server_id: str, reply_data: Optional[ReplyContext]
    ) -> Tuple[str, discord.AllowedMentions]:
        reply_mention = ''
        replied_user_id = None
        if reply_data and is_reply_mention_required(target_server_id, reply_data.message.guildId):
            replied_user_id = int(reply_data.message.authorId)
            reply_mention = f'<@{replied_user_id}> '

        badge_header = f'-# {badge_prefix}\n' if badge_prefix else ''
        final_content = f'{badge_header}{reply_mention}{content}'
        allowed_mentions = filter_user_mentions(replied_user_id)
        return final_content, allowed_mentions

    async def _dispatch_broadcasts(
        self,
        message: discord.Message,
        prerequisites: BroadcastPrerequisites,
        payload: BroadcastPayload,
    ) -> List[SentMessageInfo]:
        tasks = [
            self._send_single_broadcast(conn, message, payload, prerequisites)
            for conn in prerequisites.other_connections
        ]
        results = await asyncio.gather(*tasks)
        successful_sends = [res for res in results if res]

        hub_id = prerequisites.other_connections[0].hubId if prerequisites.other_connections else 'N/A'
        logger.info(
            f'Broadcast summary for Hub {hub_id}: {len(successful_sends)} successful, '
            f'{len(prerequisites.other_connections) - len(successful_sends)} failed.'
        )
        return successful_sends

    async def _test_and_swap_back_webhook(self, connection: Connection, message: discord.Message) -> None:
        if not connection.webhookSecondaryURL:
            return

        redis_key = f'interchat:hub:{connection.hubId}:last_context'
        last_context_from_redis = await redis_client.get(redis_key)
        last_context = last_context_from_redis if last_context_from_redis else None
        last_author_id = last_context.split(':')[0] if last_context else None
        current_author_id = str(message.author.id)

        if last_author_id == current_author_id:
            return

        try:
            async with self.bot.http_session.head(connection.webhookSecondaryURL) as resp:
                if resp.status == 200:
                    logger.info(f'Original primary webhook for conn {connection.id} is now healthy. Restoring it.')
                    primary_url = connection.webhookURL
                    connection.webhookURL = connection.webhookSecondaryURL
                    connection.webhookSecondaryURL = primary_url
        except Exception as e:
            logger.debug(f'Health check failed for demoted webhook on conn {connection.id}: {e}')

    async def _send_single_broadcast(
        self,
        connection: Connection,
        message: discord.Message,
        payload: BroadcastPayload,
        prerequisites: BroadcastPrerequisites,
    ) -> Optional[SentMessageInfo]:
        assert message.guild is not None, 'Message must be from a guild'

        await self._test_and_swap_back_webhook(connection, message)

        if connection.server and check_if_blocked(connection.server, message.author.id, message.guild.id):
            return None

        content, allowed_mentions = self._format_final_content(
            payload.content, payload.badge_prefix, connection.serverId, payload.reply_data
        )
        final_payload = dataclasses.replace(payload, content=content)
        reply_embed = self._create_reply_embed_if_needed(connection, payload, prerequisites)
        embeds = [reply_embed] if reply_embed else []
        thread = discord.Object(id=int(connection.channelId)) if connection.parentId else None

        sent_message = None

        try:
            webhook = discord.Webhook.from_url(url=connection.webhookURL, session=self.bot.http_session)
            sent_message = await self._send_webhook_with_retry(webhook, final_payload, allowed_mentions, thread, embeds)

        except (discord.NotFound, discord.HTTPException) as e:
            is_rate_limited = isinstance(e, discord.HTTPException) and e.status == 429
            is_not_found = isinstance(e, discord.NotFound)

            if not (is_rate_limited or is_not_found):
                logger.error(f'Unhandled Discord HTTP error for connection {connection.id}: {str(e)}.')
                return None

            log_reason = 'rate limited' if is_rate_limited else 'not found'
            logger.warning(f'Primary webhook for conn {connection.id} is {log_reason}. Attempting fallback.')

            if not connection.webhookSecondaryURL:
                logger.error(f'Primary webhook failed for conn {connection.id} and no secondary exists. Disconnecting.')
                return None

            try:
                fallback_webhook = discord.Webhook.from_url(
                    url=connection.webhookSecondaryURL, session=self.bot.http_session
                )
                sent_message = await self._send_webhook_with_retry(
                    fallback_webhook, final_payload, allowed_mentions, thread, embeds
                )
                if sent_message:
                    logger.info(f'Secondary webhook for conn {connection.id} succeeded. Promoting to primary.')
                    primary_url = connection.webhookURL
                    connection.webhookURL = connection.webhookSecondaryURL
                    connection.webhookSecondaryURL = primary_url

            except (discord.NotFound, discord.Forbidden, discord.HTTPException) as fallback_e:
                logger.error(f'Secondary webhook for conn {connection.id} also failed: {fallback_e}. Disconnecting.')
                return None

        except Exception as e:
            logger.error(f'Unexpected error broadcasting to connection {connection.id}: {e}', exc_info=True)
            return None

        if sent_message:
            return str(sent_message.id), connection.channelId, connection.serverId

        return None

    @ExponentialBackoff()
    async def _send_webhook_with_retry(
        self,
        webhook: discord.Webhook,
        payload: BroadcastPayload,
        allowed_mentions: discord.AllowedMentions,
        thread: Optional[discord.Object] = None,
        embeds: Optional[list[discord.Embed]] = None,
    ) -> Optional[discord.WebhookMessage]:
        try:
            # Final safety check: ensure content is never empty
            content = payload.content
            if not content or not content.strip():
                # If we have embeds, we can send without content
                if embeds:
                    content = None
                else:
                    # Last resort fallback to prevent empty message error
                    content = '*[Message]*'
                    logger.warning(
                        f"Empty content detected for user '{payload.author_name}' (connection webhook), "
                        f"using fallback content. This indicates a bug in content processing."
                    )

            kwargs = {
                'content': content,
                'username': payload.author_name,
                'avatar_url': payload.author_avatar_url,
                'allowed_mentions': allowed_mentions,
                'wait': True,
            }
            if thread:
                kwargs['thread'] = thread
            if embeds:
                kwargs['embeds'] = embeds
            return await webhook.send(**kwargs)
        except Exception:
            logger.warning(f"Webhook send failed for user '{payload.author_name}'. Retrying...", exc_info=True)
            raise

    def _create_reply_embed_if_needed(
        self, connection: Connection, payload: BroadcastPayload, prereqs: BroadcastPrerequisites
    ) -> Optional[discord.Embed]:
        if not payload.reply_data or payload.attachment_urls:
            return None

        try:
            precomputed_id = (prereqs.channel_to_broadcast_id or {}).get(connection.channelId)
            broadcasted_message_id = precomputed_id or payload.reply_data.message.id
            original_message_tuple = (payload.reply_data.message, payload.reply_data.user, '')

            message_reference = discord.MessageReference(
                message_id=int(payload.reply_data.message.id),
                channel_id=int(payload.reply_data.message.channelId),
                guild_id=int(payload.reply_data.message.guildId),
            )

            return self.message_svc.create_reply_embed(
                message_reference=message_reference,
                reply_data=original_message_tuple,
                target_channel_id=connection.channelId,
                target_server_id=connection.serverId,
                broadcast_message_id=broadcasted_message_id,
            )
        except Exception as e:
            logger.warning(f'Failed to create reply embed for channel {connection.channelId}: {e}')
            return None

    @staticmethod
    async def _get_hub_context(session: AsyncSession, channel_id: int, author: discord.Member) -> Optional[HubContext]:
        stmt = (
            select(Connection, Hub, HubRulesAcceptance)
            .join(Hub, Connection.hubId == Hub.id)
            .outerjoin(
                HubRulesAcceptance,
                and_(HubRulesAcceptance.hubId == Hub.id, HubRulesAcceptance.userId == str(author.id)),
            )
            .where(Connection.channelId == str(channel_id), Connection.connected.is_(True))
            .options(joinedload(Connection.server))
        )
        result = (await session.execute(stmt)).first()
        return HubContext(result[0], result[1], result[2], author) if result else None

    @staticmethod
    async def _get_other_connections(
        session: AsyncSession, hub_id: str, source_channel_id: str
    ) -> Sequence[Connection]:
        stmt = (
            select(Connection)
            .where(
                Connection.hubId == hub_id,
                Connection.channelId != source_channel_id,
                Connection.connected.is_(True),
            )
            .options(joinedload(Connection.server).selectinload(ServerData.blocklist))
        )
        result = await session.execute(stmt)
        return result.scalars().all()

    @staticmethod
    async def _fetch_reply_data(session: AsyncSession, message: discord.Message) -> Optional[Tuple[Message, User, str]]:
        if message.reference and message.reference.message_id:
            return await fetch_original_msg_with_extra(session, str(message.reference.message_id))
        return None

    @staticmethod
    async def _update_analytics_counts(session: AsyncSession, message: discord.Message) -> None:
        await UserManager.update_user_activity(str(message.author.id), session)

        if message.guild:
            try:
                server = await session.get(ServerData, str(message.guild.id))
                if server:
                    server.messageCount += 1
                    server.lastMessageAt = func.now()
                else:
                    session.add(
                        ServerData(
                            id=str(message.guild.id),
                            name=message.guild.name,
                            messageCount=1,
                            lastMessageAt=func.now(),
                            iconUrl=message.guild.icon.url if message.guild.icon else None,
                        )
                    )
            except Exception as e:
                logger.warning(f'Failed to stage server message count update: {e}')

    @staticmethod
    def _user_must_accept_rules(hub: Hub, acceptance: Optional[HubRulesAcceptance]) -> bool:
        return bool(hub.rules) and not acceptance

    async def _send_welcome_rules(self, message: discord.Message, hub: Hub) -> None:
        rules_text = '\n'.join(f'**{i}.** {rule}' for i, rule in enumerate(hub.rules, 1))
        if len(rules_text) > 4000:
            rules_text = rules_text[:3997] + '...'

        embed = discord.Embed(
            title=f'Welcome to {hub.name}! 📋',
            description=f'Hi {message.author.mention}, please read and accept our rules to participate:\n\n{rules_text}',
            color=self.bot.constants.color,
        )
        view = HubRulesAcceptanceView(self.bot, message.author, hub, str(message.channel.id))
        await message.channel.send(embed=embed, view=view)
