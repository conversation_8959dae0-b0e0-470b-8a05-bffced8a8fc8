import functools
import time
from datetime import datetime, timed<PERSON>ta
from typing import TYPE_CHECKING, Final, Optional

import discord
from sqlalchemy.ext.asyncio import AsyncSession

from utils.constants import logger, redis_client
from utils.modules.broadcast.validation.utils.interfaces import SpamAction
from utils.modules.core.antiSpam import SpamDetectionResult
from utils.modules.core.db.models import Hub, InfractionType

from utils.modules.moderation.services import ModerationService

if TYPE_CHECKING:
    from main import Bot


def time_execution(func):
    """A decorator to log the execution time of an async function."""

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        result = await func(*args, **kwargs)
        end_time = time.perf_counter()
        total_time_ms = (end_time - start_time) * 1000
        logger.debug(f'Execution time for {args[0].__class__.__name__}.{func.__name__}: {total_time_ms:.2f}ms')
        return result

    return wrapper


class SpamService:
    RATE_LIMIT_MESSAGES: Final[int] = 5
    RATE_LIMIT_PERIOD: Final[int] = 5
    MUTE_DURATION_MINUTES: Final[int] = 10
    WARNING_THRESHOLD: Final[int] = 2
    WARNING_EXPIRY_SECONDS: Final[int] = 3600

    MESSAGE_COUNT_KEY: Final[str] = 'spam_check:{user_id}:{hub_id}'
    WARNING_COUNT_KEY: Final[str] = 'spam_warnings:{user_id}:{hub_id}'

    def __init__(self):
        self.redis = redis_client

    def _get_redis_keys(self, user_id: str, hub_id: str) -> tuple[str, str]:
        """Generate Redis keys for a user in a hub."""
        return (
            self.MESSAGE_COUNT_KEY.format(user_id=user_id, hub_id=hub_id),
            self.WARNING_COUNT_KEY.format(user_id=user_id, hub_id=hub_id),
        )

    async def _ensure_moderator_exists(self, bot: 'Bot', session: AsyncSession) -> None:
        """
        Ensures the bot (acting as the moderator for auto-actions) has a record
        in the User database table to prevent foreign key violations.
        """
        if not bot.user:
            logger.error('Cannot ensure moderator exists: bot.user is not available.')
            return

    @time_execution
    async def check_spam(self, user_id: str, hub_id: str) -> SpamDetectionResult:
        """Checks for spam using Redis."""
        message_key, warning_key = self._get_redis_keys(user_id, hub_id)
        pipe = self.redis.pipeline()
        pipe.incr(message_key)
        pipe.ttl(message_key)
        message_count, ttl = await pipe.execute()

        if ttl == -1:
            await self.redis.expire(message_key, self.RATE_LIMIT_PERIOD)
        if message_count <= self.RATE_LIMIT_MESSAGES:
            return SpamDetectionResult(is_spam=False, message_count=message_count)

        pipe = self.redis.pipeline()
        pipe.incr(warning_key)
        pipe.ttl(warning_key)
        new_warning_count, warning_ttl = await pipe.execute()
        previous_warning_count = new_warning_count - 1

        if warning_ttl == -1:
            await self.redis.expire(warning_key, self.WARNING_EXPIRY_SECONDS)
        if previous_warning_count >= self.WARNING_THRESHOLD:
            mute_expires_at = datetime.now() + timedelta(minutes=self.MUTE_DURATION_MINUTES)
            return SpamDetectionResult(
                is_spam=True,
                action_needed=SpamAction.MUTE,
                message_count=message_count,
                warning_message=(
                    f'You have been temporarily muted for {self.MUTE_DURATION_MINUTES} minutes due to spam. '
                    f'Your mute expires at <t:{int(mute_expires_at.timestamp())}:F>.'
                ),
            )
        return SpamDetectionResult(
            is_spam=True,
            action_needed=SpamAction.WARN,
            message_count=message_count,
            warning_message='⚠️ You are sending messages too quickly! Please slow down or you will be temporarily muted.',
        )

    @time_execution
    async def mute_user_for_spam(
        self, bot: 'Bot', user: discord.User | discord.Member, hub: Hub, session: AsyncSession
    ) -> Optional[str]:
        """Creates a mute infraction using the provided session."""
        try:
            await self._ensure_moderator_exists(bot, session)

            bot_user_id = str(bot.user.id if bot.user else 0)
            reason = f'Auto-Mute: Spam detected - exceeded {self.RATE_LIMIT_MESSAGES} messages in {self.RATE_LIMIT_PERIOD} seconds'
            duration_ms = self.MUTE_DURATION_MINUTES * 60 * 1000

            moderation_service = ModerationService(session)
            infraction = await moderation_service.create_infraction(
                hub_id=hub.id,
                mod_id=bot_user_id,
                user_id=str(user.id),
                reason=reason,
                infraction_type=InfractionType.MUTE,
                duration_ms=duration_ms,
            )

            return infraction.id if infraction else None
        except ValueError as e:
            if str(e) == 'DUPLICATE_BAN_OR_MUTE':
                logger.debug(f'User {user.id} is already muted in hub {hub.id}. Fetching existing infraction.')
                existing_infraction = await ModerationService(session).get_active_mute(hub.id, str(user.id))
                return existing_infraction.id if existing_infraction else None
            else:
                raise
        except Exception as e:
            await session.rollback()
            logger.error(f'Failed to mute user {user.id} for spam in hub {hub.id}: {e}', exc_info=True)
            return None

    @time_execution
    async def warn_user_for_spam(
        self, bot: 'Bot', user: discord.User | discord.Member, hub: Hub, session: AsyncSession
    ) -> Optional[str]:
        """Creates a warning infraction using the provided session."""
        try:
            await self._ensure_moderator_exists(bot, session)

            bot_user_id = str(bot.user.id if bot.user else 0)
            reason = f'Auto-Warn: Spam detected - exceeded {self.RATE_LIMIT_MESSAGES} messages in {self.RATE_LIMIT_PERIOD} seconds'

            moderation_service = ModerationService(session)
            infraction = await moderation_service.create_infraction(
                hub_id=hub.id,
                mod_id=bot_user_id,
                user_id=str(user.id),
                reason=reason,
                infraction_type=InfractionType.WARNING,
            )

            return infraction.id if infraction else None
        except Exception as e:
            await session.rollback()
            logger.error(f'Failed to warn user {user.id} for spam in hub {hub.id}: {e}', exc_info=True)
            return None

    async def clear_spam_tracking(self, user_id: str, hub_id: str) -> None:
        """Clear all spam tracking data for a user in a hub."""
        try:
            message_key, warning_key = self._get_redis_keys(user_id, hub_id)
            await self.redis.delete(message_key, warning_key)
        except Exception as e:
            logger.warning(f'Failed to clear spam tracking for user {user_id} in hub {hub_id}: {e}')

    async def get_warning_count(self, user_id: str, hub_id: str) -> int:
        """Public method to get the current warning count for a user."""
        try:
            _, warning_key = self._get_redis_keys(user_id, hub_id)
            count = await self.redis.get(warning_key)
            return int(count) if count else 0
        except Exception as e:
            logger.warning(f'Failed to get warning count for user {user_id} in hub {hub_id}: {e}')
            return 0

    async def reset_message_count(self, user_id: str, hub_id: str) -> None:
        """Reset only the message count for a user."""
        try:
            message_key, _ = self._get_redis_keys(user_id, hub_id)
            await self.redis.delete(message_key)
        except Exception as e:
            logger.warning(f'Failed to reset message count for user {user_id} in hub {hub_id}: {e}')
