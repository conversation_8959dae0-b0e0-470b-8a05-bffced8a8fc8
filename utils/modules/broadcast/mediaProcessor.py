import re
from typing import Final, Tuple, List, Optional
from urllib.parse import urlparse

import discord


class MediaProcessor:
    URL_PATTERN: Final = re.compile(r'https?://\S+')
    # Matches [⁥](url). We use a special invisible character to hide the URL.
    IMAGE_URL_PATTERN: Final = re.compile(r'\[⁥]\(([^)]+)\)')

    ALLOWED_IMAGE_EXTENSIONS: Final = frozenset(['.png', '.jpg', '.jpeg', '.webp', '.avif'])
    ALLOWED_GIF_EXTENSION: Final = '.gif'
    ALLOWED_STICKER_EXTENSIONS: Final = frozenset(['.png', '.jpg', '.jpeg', '.webp', '.avif', '.apng'])

    TENOR_DOMAIN: Final = 'tenor.com'

    # Future support planned - videos (currently blocked but will be allowed later)
    # VIDEO_EXTENSIONS: Final = frozenset(['.mp4', '.mov', '.avi', '.webm', '.mkv'])

    def process_media(
        self,
        content: str,
        attachments: List[discord.Attachment],
        stickers: List[discord.StickerItem],
    ) -> Tuple[str, List[str]]:
        """Process message media - BLOCKS all non-allowed formats"""
        content_parts = [content or '']
        urls = []
        has_blocked_attachment = False

        for attachment in attachments:
            if self._is_allowed_attachment(attachment):
                urls.append(attachment.url)
                content_parts.append(f'[⁥]({attachment.url})')
            else:
                # Block all non-allowed attachments
                has_blocked_attachment = True
                content_parts.append('[Attachment blocked - only images and GIFs allowed]')

        # Only add stickers (they're always allowed as they're controlled by Discord)
        content_parts.extend(f'[⁥]({sticker.url})' for sticker in stickers)

        processed_content = '\n'.join(part for part in content_parts if part.strip())

        # Handle URLs in content (block non-Tenor GIFs)
        found_urls = self.URL_PATTERN.findall(processed_content)
        blocked_urls = []

        for url in found_urls:
            try:
                if url not in urls:  # Avoid duplicates
                    urls.append(url)
                if self.is_gif_url(url) and not self.is_tenor_gif(url):
                    blocked_urls.append(url)
            except (ValueError, AttributeError, TypeError):
                continue

        for blocked_url in blocked_urls:
            processed_content = processed_content.replace(blocked_url, '[GIF blocked - only Tenor GIFs allowed]', 1)

        # Ensure we have content when attachments/stickers exist
        if not processed_content.strip() and (attachments or stickers):
            if has_blocked_attachment:
                processed_content = '[Message contained blocked attachments]'
            else:
                processed_content = '*[Media attachment]*'

        return processed_content, urls

    def _is_allowed_attachment(self, attachment: discord.Attachment) -> bool:
        """Check if attachment is in the allowlist of supported formats"""
        filename = attachment.filename.lower() if attachment.filename else ''

        # Check if it's an allowed image format
        if any(filename.endswith(ext) for ext in self.ALLOWED_IMAGE_EXTENSIONS):
            return True

        # Check if it's a GIF
        if filename.endswith(self.ALLOWED_GIF_EXTENSION):
            return True

        # All other formats are blocked
        return False

    @staticmethod
    def extract_image_url_from_content(content: str) -> Optional[str]:
        """Extract image URL from content using pattern matching"""
        if '[⁥](' in content:
            match = MediaProcessor.IMAGE_URL_PATTERN.search(content)
            if match:
                return match.group(1)
        return None

    @classmethod
    def is_image_url(cls, url: str) -> bool:
        """Check if URL is an allowed image format."""
        try:
            parsed_url = urlparse(url.lower())
            return any(parsed_url.path.endswith(ext) for ext in cls.ALLOWED_IMAGE_EXTENSIONS)
        except (ValueError, AttributeError):
            return False

    @classmethod
    def is_gif_url(cls, url: str) -> bool:
        """Check if URL is a GIF."""
        try:
            parsed_url = urlparse(url.lower())
            return parsed_url.path.endswith(cls.ALLOWED_GIF_EXTENSION)
        except (ValueError, AttributeError):
            return False

    @staticmethod
    def is_tenor_gif(url: str) -> bool:
        """Check if URL is from Tenor."""
        return MediaProcessor.TENOR_DOMAIN in url.lower()

    @staticmethod
    def extract_static_image_urls(message: discord.Message) -> List[str]:
        """Extract static image URLs (png, jpg, jpeg, webp) and static stickers from message."""
        seen = set()
        urls = []

        # Process attachments
        attachments = message.attachments
        if attachments:
            for att in attachments:
                url = att.url
                if url and url not in seen and not MediaProcessor.is_gif_url(url) and MediaProcessor.is_image_url(url):
                    urls.append(url)
                    seen.add(url)

        # Process embeds
        embeds = message.embeds
        if embeds:
            for emb in embeds:
                # Check image
                image = emb.image
                if image:
                    url = image.url
                    if (
                        url
                        and url not in seen
                        and not MediaProcessor.is_gif_url(url)
                        and MediaProcessor.is_image_url(url)
                    ):
                        urls.append(url)
                        seen.add(url)

                # Check thumbnail
                thumbnail = emb.thumbnail
                if thumbnail:
                    url = thumbnail.url
                    if (
                        url
                        and url not in seen
                        and not MediaProcessor.is_gif_url(url)
                        and MediaProcessor.is_image_url(url)
                    ):
                        urls.append(url)
                        seen.add(url)

        # Process stickers
        stickers = message.stickers
        if stickers:
            try:
                png_format = discord.StickerFormatType.png
                for sticker in stickers:
                    if sticker.format == png_format:
                        url = sticker.url
                        if url and url not in seen:
                            urls.append(url)
                            seen.add(url)
            except (AttributeError, Exception):
                pass

        return urls
