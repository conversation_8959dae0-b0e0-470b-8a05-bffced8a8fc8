from __future__ import annotations

from dataclasses import dataclass
from typing import Dict, Optional, Sequence

import discord

from utils.modules.core.db.models import Connection, Hub, HubRulesAcceptance, Message, User


@dataclass
class BroadcastPayload:
    """Encapsulates all data required to send a single broadcast message."""

    content: str
    author_name: str
    author_avatar_url: str
    badge_prefix: str
    reply_data: Optional[ReplyContext]
    attachment_urls: Optional[list[str]] = None
    reply_embed: Optional[discord.Embed] = None

    def __post_init__(self):
        if self.attachment_urls is None:
            self.attachment_urls = []


@dataclass
class BroadcastPrerequisites:
    """Holds all the data gathered concurrently before a broadcast."""

    other_connections: Sequence[Connection]
    badge_prefix: str
    reply_data: Optional[ReplyContext]
    channel_to_broadcast_id: Optional[Dict[str, str]] = None

    def __post_init__(self):
        if self.channel_to_broadcast_id is None:
            self.channel_to_broadcast_id = {}


@dataclass
class HubContext:
    """Provides contextual information about the hub and the message author."""

    connection: Connection
    hub: Hub
    rules_acceptance: Optional[HubRulesAcceptance]
    author: discord.Member


@dataclass
class ReplyContext:
    """Contains information about a message being replied to."""

    message: Message
    user: User
    hub_id: str
