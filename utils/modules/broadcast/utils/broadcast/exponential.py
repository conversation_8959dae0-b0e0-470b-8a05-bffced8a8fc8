import asyncio
import random
from functools import wraps
from typing import Awaitable, Callable, Any, Optional

import aiohttp
import discord

# LOGGING IMPROVEMENT: Import the logger to be used within the class.
from utils.constants import logger


class ExponentialBackoff:
    """
    A decorator for retrying an async function with exponential backoff and jitter.

    This decorator intelligently handles Discord API errors and transient network issues.
    It will retry on:
    - Rate limits (429), respecting the 'retry_after' header.
    - Server-side errors (5xx).
    - Connection errors, timeouts, and client-side network issues.

    It will NOT retry on:
    - Client-side errors like Not Found (404) or Forbidden (403).
    """

    def __init__(
        self,
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 30.0,
        backoff_factor: float = 2.0,
    ):
        """
        Initializes the exponential backoff decorator.

        Args:
            max_retries: The maximum number of times to retry the function.
            base_delay: The initial delay in seconds for the first retry.
            max_delay: The maximum possible delay between retries.
            backoff_factor: The multiplier for increasing the delay (e.g., 2 for doubling).
        """
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor

    def __call__(self, func: Callable[..., Awaitable[Any]]) -> Callable[..., Awaitable[Any]]:
        """Makes the class instance a callable decorator."""

        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            """The wrapper that executes the retry logic."""
            last_exception = None
            for attempt in range(self.max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except (
                    discord.HTTPException,
                    aiohttp.ClientError,
                    asyncio.TimeoutError,
                    ConnectionError,
                ) as e:
                    last_exception = e

                    if attempt >= self.max_retries:
                        logger.error(f"Function '{func.__name__}' failed after {self.max_retries + 1} attempts. Final error: {e}", exc_info=True)
                        raise

                    delay = self._get_delay(e, attempt)

                    if delay is None:  # Indicates a non-retryable error
                        logger.warning(f"Function '{func.__name__}' encountered a non-retryable error: {e}. Failing immediately without retries.")
                        raise

                    logger.warning(
                        f"Function '{func.__name__}' failed with {type(e).__name__} "
                        f'(Attempt {attempt + 1}/{self.max_retries}). Retrying in {delay:.2f} seconds.'
                    )
                    await asyncio.sleep(delay)

            # This fallback should ideally not be reached, but is here for safety.
            if last_exception:
                logger.error(f"Function '{func.__name__}' failed unexpectedly after retry loop.", exc_info=last_exception)
                raise last_exception

        return wrapper

    def _get_delay(self, error: Exception, attempt: int) -> Optional[float]:
        """
        Determines the appropriate delay based on the error type.

        Returns None if the error should not be retried.
        """
        if isinstance(error, discord.HTTPException):
            # For rate limits, use the provided 'retry_after' if available.
            if error.status == 429:
                retry_after = getattr(error, 'retry_after', None)
                if retry_after is not None:
                    # Add a small buffer to the official retry_after time
                    return float(retry_after) + 0.5
                return self._calculate_backoff(attempt)

            # For server-side errors (5xx), perform a standard backoff.
            if 500 <= error.status < 600:
                return self._calculate_backoff(attempt)

            # For other client-side errors (4xx like 403, 404), do not retry.
            return None

        # For general network errors, perform a standard backoff.
        if isinstance(error, (aiohttp.ClientError, asyncio.TimeoutError, ConnectionError)):
            return self._calculate_backoff(attempt)

        # For any other unexpected exception type, do not retry.
        return None

    def _calculate_backoff(self, attempt: int) -> float:
        """Calculates the exponential backoff delay with jitter."""
        # Calculate exponential delay
        delay = self.base_delay * (self.backoff_factor**attempt)

        # Apply jitter (randomness to prevent thundering herd problem)
        jitter = delay * 0.2  # 20% jitter
        random_jitter = random.uniform(-jitter, jitter)

        # Cap the delay at the maximum value and add jitter
        return min(delay, self.max_delay) + random_jitter
