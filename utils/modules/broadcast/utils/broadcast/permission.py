from functools import wraps
from typing import Awaitable, Callable, Any

import discord

from utils.constants import logger


def require_channel_permissions(required_perms: discord.Permissions):
    """
    A decorator that checks if the bot has the required permissions in a channel.

    This decorator assumes it is wrapping an async method of a class. The decorated
    method's first argument after 'self' must be an object with a 'channelId'
    attribute.
    """
    if not required_perms:
        raise ValueError('At least one permission must be provided.')

    def decorator(func: Callable[..., Awaitable[Any]]) -> Callable[..., Awaitable[Any]]:
        @wraps(func)
        async def wrapper(self: Any, connection_obj: Any, *args: Any, **kwargs: Any) -> Any:
            bot = self.bot

            if not hasattr(connection_obj, 'channelId'):
                logger.error(
                    f"Decorator '{func.__name__}' failed: The provided object of type '{type(connection_obj).__name__}' has no 'channelId' attribute."
                )
                return None

            try:
                channel_id = int(connection_obj.channelId)
                channel = bot.get_channel(channel_id)
                # If channel is not in cache, try to fetch it from the API.
                if not channel:
                    channel = await bot.fetch_channel(channel_id)
            except (ValueError, TypeError):
                logger.error(
                    f"Permission check failed for connection {getattr(connection_obj, 'id', 'N/A')}: Invalid channelId '{connection_obj.channelId}'."
                )
                return None
            except discord.NotFound:
                logger.warning(
                    f'Permission check failed for connection {getattr(connection_obj, "id", "N/A")}: '
                    f'Channel {connection_obj.channelId} could not be found (likely deleted).'
                )
                return None
            except discord.Forbidden:
                logger.error(
                    f'Permission check failed for connection {getattr(connection_obj, "id", "N/A")}: '
                    f'Forbidden to fetch channel {connection_obj.channelId}. Bot may have been kicked.'
                )
                return None

            if not isinstance(channel, (discord.TextChannel, discord.Thread)):
                logger.warning(
                    f'Permission check skipped for connection {getattr(connection_obj, "id", "N/A")}: '
                    f'Channel {channel_id} is not a TextChannel or Thread.'
                )
                return None

            channel_perms = channel.permissions_for(channel.guild.me)

            if not channel_perms.is_superset(required_perms):
                missing_perms = [name for name, enabled in required_perms if enabled]
                logger.error(
                    f'Broadcast failed for connection {getattr(connection_obj, "id", "N/A")} '
                    f'(Hub: {getattr(connection_obj, "hubId", "N/A")}, '
                    f'Server: {getattr(connection_obj, "serverId", "N/A")}, '
                    f'Channel: {connection_obj.channelId}). '
                    f'Required permissions are missing: {", ".join(missing_perms)}'
                )
                return None

            # If all checks pass, execute the original function.
            return await func(self, connection_obj, *args, **kwargs)

        return wrapper

    return decorator
