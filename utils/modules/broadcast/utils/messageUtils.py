from typing import TYPE_CHECKING, Dict, List, Optional, <PERSON><PERSON>

import discord
from sqlalchemy import and_, select

from utils.modules.broadcast.messageService import MessageService
from utils.modules.core.db.models import Broadcast, Hub, Message

if TYPE_CHECKING:
    from sqlalchemy.ext.asyncio import AsyncSession


def is_reply_mention_required(current_server_id: str, original_server_id: str) -> bool:
    if current_server_id == original_server_id:
        return True
    return False


async def get_broadcast_ids_for_channels(
    session: 'AsyncSession', original_message_id: str, channel_ids: List[str]
) -> Dict[str, str]:
    if not channel_ids:
        return {}

    stmt = select(Broadcast.channelId, Broadcast.id).where(
        and_(Broadcast.messageId == original_message_id, Broadcast.channelId.in_(channel_ids))
    )

    result = await session.execute(stmt)
    mapping = {channel_id: broadcast_id for channel_id, broadcast_id in result.tuples()}

    return mapping


async def store_message_and_broadcasts(
    message: discord.Message,
    hub: Hub,
    processed_content: str,
    broadcast_message_ids: List[Tuple[str, str, str]],
    session: 'AsyncSession',
    referred_message: Optional[Message] = None,
) -> None:
    try:
        message_obj = MessageService.create_message_object(message, hub, processed_content, referred_message)
        session.add(message_obj)

        if broadcast_message_ids:
            broadcast_objects = [
                Broadcast(
                    id=broadcast_id,
                    messageId=str(message.id),
                    channelId=channel_id,
                    guildId=guild_id,
                )
                for broadcast_id, channel_id, guild_id in broadcast_message_ids
            ]

            session.add_all(broadcast_objects)

        await session.commit()

    except Exception as e:
        await session.rollback()
        raise RuntimeError(f'Failed to store message and broadcasts: {e}') from e
