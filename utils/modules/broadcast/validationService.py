import asyncio
from typing import T<PERSON><PERSON>_CHECKING

import discord
from sqlalchemy import exists, select
from sqlalchemy.ext.asyncio import AsyncSession

from utils.constants import logger
from utils.modules.broadcast.validation import (
    ValidationResult,
    validate_access_restrictions,
    validate_account_age,
    validate_hub_status,
    validate_nsfw_images,
    validate_profanity,
    validate_spam,
    validate_message_length,
)
from utils.modules.core.db.models import Connection, Hub

if TYPE_CHECKING:
    from main import Bot
    from utils.modules.broadcast.spamService import SpamService


class ValidationService:
    def __init__(self, spam_service: 'SpamService'):
        self.spam_service = spam_service

    @staticmethod
    async def validate_connection(bot: 'Bot', channel: discord.abc.GuildChannel) -> bool:
        """Validate if a connection exists for the given channel."""
        async with bot.db.get_session() as session:
            stmt = select(exists().where(Connection.channelId == str(channel.id)))
            return await session.scalar(stmt) or False

    async def validate_message(
        self,
        message: discord.Message,
        hub: Hub,
        user: discord.Member | discord.User,
        guild: discord.Guild,
        session: AsyncSession,
    ) -> ValidationResult:
        sync_checks = [
            validate_message_length(message.content, 1000),
            validate_hub_status(hub),
            validate_account_age(user),
        ]
        for result in sync_checks:
            if not result.is_valid:
                logger.info(f'Message {message.id} failed fast sync validation: {result.reason}')
                return result

        io_validation_tasks = [
            validate_spam(str(user.id), str(hub.id), self.spam_service),
            validate_profanity(message, hub, session),
            validate_access_restrictions(str(user.id), str(guild.id), hub.id, session),
            validate_nsfw_images(message, hub),
        ]

        results = await asyncio.gather(*io_validation_tasks, return_exceptions=True)

        for result in results:
            if isinstance(result, BaseException):
                logger.error(
                    f'An I/O validation check for message {message.id} raised an exception: {result}',
                    exc_info=result,
                )
                return ValidationResult.failure('An internal error occurred during validation.')

            if not result.is_valid:
                logger.info(
                    f'Message {message.id} failed I/O validation: {result.reason} (should_notify={result.should_notify})'
                )
                return result

        return ValidationResult(is_valid=True)
