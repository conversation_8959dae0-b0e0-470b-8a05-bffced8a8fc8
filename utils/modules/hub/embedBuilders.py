from typing import TYPE_CHECKING
import discord

from utils.modules.events.eventDispatcher import <PERSON>b<PERSON>vent, HubEventType

if TYPE_CHECKING:
    from utils.modules.emojis.EmojiManager import EmojiManager


class ModerationEmbedBuilder:
    def __init__(self, emotes: 'EmojiManager'):
        self.emotes = emotes

    def build_ban_embed(self, event: <PERSON>b<PERSON><PERSON>) -> discord.Embed:
        is_user = event.target_user_id is not None
        target_type = 'User' if is_user else 'Server'
        target_name = event.target_user_name if is_user else event.target_server_name
        target_id = event.target_user_id if is_user else event.target_server_id

        embed = discord.Embed(
            title=f'{self.emotes.hammer_icon} {target_type} Banned',
            description=f'A {target_type.lower()} has been banned from the hub.',
            color=discord.Color.red(),
            timestamp=event.timestamp,
        )

        embed.add_field(
            name=f'Banned {target_type}', value=f'**{target_name}**\n`{target_id}`', inline=True
        )

        if event.moderator_name:
            embed.add_field(
                name='Moderator',
                value=f'**{event.moderator_name}**\n`{event.moderator_id}`',
                inline=True,
            )

        embed.add_field(name='Hub', value=f'**{event.hub_name}**\n`{event.hub_id}`', inline=True)

        if event.reason:
            embed.add_field(name='Reason', value=event.reason[:1024], inline=False)

        if event.duration:
            embed.add_field(name='Duration', value=event.duration, inline=True)

        if event.expires_at:
            embed.add_field(
                name='Expires', value=f'<t:{int(event.expires_at.timestamp())}:F>', inline=True
            )

        embed.set_footer(text='Users can appeal this decision using /appeal')
        return embed

    def build_unban_embed(self, event: HubEvent) -> discord.Embed:
        is_user = event.target_user_id is not None
        target_type = 'User' if is_user else 'Server'
        target_name = event.target_user_name if is_user else event.target_server_name
        target_id = event.target_user_id if is_user else event.target_server_id

        embed = discord.Embed(
            title=f'{self.emotes.tick} {target_type} Unbanned',
            description=f"A {target_type.lower()}'s ban has been revoked.",
            color=discord.Color.green(),
            timestamp=event.timestamp,
        )

        embed.add_field(
            name=f'Unbanned {target_type}', value=f'**{target_name}**\n`{target_id}`', inline=True
        )

        if event.moderator_name:
            embed.add_field(
                name='Moderator',
                value=f'**{event.moderator_name}**\n`{event.moderator_id}`',
                inline=True,
            )

        embed.add_field(name='Hub', value=f'**{event.hub_name}**\n`{event.hub_id}`', inline=True)

        if event.reason:
            embed.add_field(name='Reason', value=event.reason[:1024], inline=False)

        return embed

    def build_mute_embed(self, event: HubEvent) -> discord.Embed:
        is_user = event.target_user_id is not None
        target_type = 'User' if is_user else 'Server'
        target_name = event.target_user_name if is_user else event.target_server_name
        target_id = event.target_user_id if is_user else event.target_server_id

        embed = discord.Embed(
            title=f'🔇 {target_type} Muted',
            description=f'A {target_type.lower()} has been muted in the hub.',
            color=discord.Color.orange(),
            timestamp=event.timestamp,
        )

        embed.add_field(
            name=f'Muted {target_type}', value=f'**{target_name}**\n`{target_id}`', inline=True
        )

        if event.moderator_name:
            embed.add_field(
                name='Moderator',
                value=f'**{event.moderator_name}**\n`{event.moderator_id}`',
                inline=True,
            )

        embed.add_field(name='Hub', value=f'**{event.hub_name}**\n`{event.hub_id}`', inline=True)

        if event.reason:
            embed.add_field(name='Reason', value=event.reason[:1024], inline=False)

        if event.duration:
            embed.add_field(name='Duration', value=event.duration, inline=True)

        if event.expires_at:
            embed.add_field(
                name='Expires', value=f'<t:{int(event.expires_at.timestamp())}:F>', inline=True
            )

        embed.set_footer(text='Users can appeal this decision using /appeal')
        return embed

    def build_unmute_embed(self, event: HubEvent) -> discord.Embed:
        is_user = event.target_user_id is not None
        target_type = 'User' if is_user else 'Server'
        target_name = event.target_user_name if is_user else event.target_server_name
        target_id = event.target_user_id if is_user else event.target_server_id

        embed = discord.Embed(
            title=f'🔊 {target_type} Unmuted',
            description=f"A {target_type.lower()}'s mute has been revoked.",
            color=discord.Color.green(),
            timestamp=event.timestamp,
        )

        embed.add_field(
            name=f'Unmuted {target_type}', value=f'**{target_name}**\n`{target_id}`', inline=True
        )

        if event.moderator_name:
            embed.add_field(
                name='Moderator',
                value=f'**{event.moderator_name}**\n`{event.moderator_id}`',
                inline=True,
            )

        embed.add_field(name='Hub', value=f'**{event.hub_name}**\n`{event.hub_id}`', inline=True)

        if event.reason:
            embed.add_field(name='Reason', value=event.reason[:1024], inline=False)

        return embed

    def build_warn_embed(self, event: HubEvent) -> discord.Embed:
        is_user = event.target_user_id is not None
        target_type = 'User' if is_user else 'Server'
        target_name = event.target_user_name if is_user else event.target_server_name
        target_id = event.target_user_id if is_user else event.target_server_id

        embed = discord.Embed(
            title=f'{self.emotes.alert_icon} {target_type} Warned',
            description=f'A {target_type.lower()} has received a warning.',
            color=discord.Color.yellow(),
            timestamp=event.timestamp,
        )

        embed.add_field(
            name=f'Warned {target_type}', value=f'**{target_name}**\n`{target_id}`', inline=True
        )

        if event.moderator_name:
            embed.add_field(
                name='Moderator',
                value=f'**{event.moderator_name}**\n`{event.moderator_id}`',
                inline=True,
            )

        embed.add_field(name='Hub', value=f'**{event.hub_name}**\n`{event.hub_id}`', inline=True)

        if event.reason:
            embed.add_field(name='Reason', value=event.reason[:1024], inline=False)

        return embed


class MessageEmbedBuilder:
    def __init__(self, emotes: 'EmojiManager'):
        self.emotes = emotes

    def build_message_delete_embed(self, event: HubEvent) -> discord.Embed:
        embed = discord.Embed(
            title=f'{self.emotes.delete_icon} Message Deleted',
            description='A message was deleted from the hub.',
            color=discord.Color.red(),
            timestamp=event.timestamp,
        )

        if event.target_user_name:
            embed.add_field(
                name='Author',
                value=f'**{event.target_user_name}**\n`{event.target_user_id}`',
                inline=True,
            )

        if event.channel_id:
            embed.add_field(name='Channel', value=f'<#{event.channel_id}>', inline=True)

        embed.add_field(name='Hub', value=f'**{event.hub_name}**', inline=True)

        if event.original_content:
            content = (
                event.original_content[:1017]
                if len(event.original_content) > 1017
                else event.original_content
            ).replace('```', '`\u200b``')

            embed.add_field(name='Deleted Content', value=f'```{content}```', inline=False)

        if event.message_id:
            embed.set_footer(text=f'Message ID: {event.message_id}')

        return embed

    def build_message_edit_embed(self, event: HubEvent) -> discord.Embed:
        embed = discord.Embed(
            title=f'{self.emotes.edit_icon} Message Edited',
            description='A message was edited in the hub.',
            color=discord.Color.blurple(),
            timestamp=event.timestamp,
        )

        if event.target_user_name:
            embed.add_field(
                name='Author',
                value=f'**{event.target_user_name}**\n`{event.target_user_id}`',
                inline=True,
            )

        if event.channel_id:
            embed.add_field(name='Channel', value=f'<#{event.channel_id}>', inline=True)

        embed.add_field(name='Hub', value=f'**{event.hub_name}**\n`{event.hub_id}`', inline=True)

        if event.original_content:
            content = (
                event.original_content[:512]
                if len(event.original_content) > 512
                else event.original_content
            ).replace('```', '`\u200b``')

            embed.add_field(name='Before', value=f'```{content}```', inline=False)

        if event.new_content:
            new_content = (
                event.new_content[:512] if len(event.new_content) > 512 else event.new_content
            )
            embed.add_field(name='After', value=f'```{new_content}```', inline=False)

        if event.message_id:
            embed.set_footer(text=f'Message ID: {event.message_id}')

        return embed


class DetectionEmbedBuilder:
    def __init__(self, emotes: 'EmojiManager'):
        self.emotes = emotes

    def build_nsfw_detection_embed(self, event: HubEvent) -> discord.Embed:
        embed = discord.Embed(
            title=f'{self.emotes.alert_icon} NSFW Content Detected',
            description='Inappropriate content was detected and blocked.',
            color=discord.Color.dark_red(),
            timestamp=event.timestamp,
        )

        if event.target_user_name:
            embed.add_field(
                name='User',
                value=f'**{event.target_user_name}**\n`{event.target_user_id}`',
                inline=True,
            )

        if event.channel_id:
            embed.add_field(name='Channel', value=f'<#{event.channel_id}>', inline=True)

        embed.add_field(name='Hub', value=f'**{event.hub_name}**\n`{event.hub_id}`', inline=False)

        # Show detection details from extra_data
        if event.extra_data and 'detected_urls' in event.extra_data:
            detected_urls = event.extra_data['detected_urls']
            if detected_urls:
                confidence_info = []
                # Show only first, because we only support 1 image as of now
                detection = detected_urls[0]
                confidence = detection.get('confidence_percentage', 0)
                confidence_info.append(f'• {confidence:.1f}% confidence')

                embed.add_field(
                    name='Detection Details', value='\n'.join(confidence_info), inline=False
                )

        embed.set_footer(text='Content was automatically blocked from broadcasting')
        return embed

    def build_profanity_detection_embed(self, event: HubEvent) -> discord.Embed:
        embed = discord.Embed(
            title=f'{self.emotes.alert_icon} Profanity Violation',
            description='A message triggered the profanity filter.',
            color=discord.Color.orange(),
            timestamp=event.timestamp,
        )

        if event.target_user_name:
            embed.add_field(
                name='User',
                value=f'**{event.target_user_name}**\n`{event.target_user_id}`',
                inline=True,
            )

        if event.channel_id:
            embed.add_field(name='Channel', value=f'<#{event.channel_id}>', inline=True)

        embed.add_field(name='Hub', value=f'**{event.hub_name}**\n`{event.hub_id}`', inline=True)

        # Show violation details from extra_data
        if event.extra_data:
            triggered_word = event.extra_data.get('triggered_word')
            rule_name = event.extra_data.get('rule_name')

            if triggered_word:
                embed.add_field(name='Triggered Word', value=f'`{triggered_word}`', inline=True)

            if rule_name:
                embed.add_field(name='Rule', value=rule_name, inline=True)

        embed.set_footer(text='Automatic moderation action may have been taken')
        return embed


class GenericEmbedBuilder:
    def __init__(self, emotes: 'EmojiManager'):
        self.emotes = emotes

    def build_generic_embed(self, event: HubEvent) -> discord.Embed:
        title, description = self._get_event_title_description(event.event_type)
        color = self._get_event_color(event.event_type)
        emoji = self._get_event_emoji(event.event_type)

        embed = discord.Embed(
            title=f'{emoji} {title}',
            description=description,
            color=color,
            timestamp=event.timestamp,
        )

        # Add hub info
        embed.add_field(name='Hub', value=f'**{event.hub_name}**\n`{event.hub_id}`', inline=True)

        # Add moderator if available
        if event.moderator_name:
            embed.add_field(
                name='Moderator',
                value=f'**{event.moderator_name}**\n`{event.moderator_id}`',
                inline=True,
            )

        # Add target info
        if event.target_user_name:
            embed.add_field(
                name='User',
                value=f'**{event.target_user_name}**\n`{event.target_user_id}`',
                inline=True,
            )
        elif event.target_server_name:
            embed.add_field(
                name='Server',
                value=f'**{event.target_server_name}**\n`{event.target_server_id}`',
                inline=True,
            )

        # Add reason if provided
        if event.reason:
            embed.add_field(name='Reason', value=event.reason[:1024], inline=False)

        return embed

    def _get_event_title_description(self, event_type: HubEventType) -> tuple[str, str]:
        mapping = {
            HubEventType.HUB_CREATE: ('Hub Created', 'A new hub was created.'),
            HubEventType.HUB_DELETE: ('Hub Deleted', 'A hub was permanently deleted.'),
            HubEventType.HUB_UPDATE: ('Hub Updated', 'Hub settings were modified.'),
            HubEventType.CONNECTION_ADD: ('Server Connected', 'A server connected to the hub.'),
            HubEventType.CONNECTION_REMOVE: (
                'Server Disconnected',
                'A server disconnected from the hub.',
            ),
        }
        return mapping.get(event_type, ('Hub Event', 'A hub-related action occurred.'))

    def _get_event_color(self, event_type: HubEventType) -> discord.Color:
        mapping = {
            HubEventType.HUB_CREATE: discord.Color.green(),
            HubEventType.HUB_DELETE: discord.Color.red(),
            HubEventType.HUB_UPDATE: discord.Color.blue(),
            HubEventType.CONNECTION_ADD: discord.Color.green(),
            HubEventType.CONNECTION_REMOVE: discord.Color.yellow(),
        }
        return mapping.get(event_type, discord.Color.blue())

    def _get_event_emoji(self, event_type: HubEventType) -> str:
        mapping = {
            HubEventType.HUB_CREATE: self.emotes.plus_icon,
            HubEventType.HUB_DELETE: self.emotes.delete,
            HubEventType.HUB_UPDATE: self.emotes.edit_icon,
            HubEventType.CONNECTION_ADD: '🔗',
            HubEventType.CONNECTION_REMOVE: '🔓',
        }
        return mapping.get(event_type, '📋')
