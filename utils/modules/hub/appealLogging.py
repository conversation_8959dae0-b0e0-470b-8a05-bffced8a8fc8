from typing import TYPE_CHECKING

import discord

from utils.modules.events.eventDispatcher import HubEvent
from utils.modules.moderation.ui.views import AppealActionView

if TYPE_CHECKING:
    from main import Bo<PERSON>


def build_appeal_embed_and_view(bot: 'Bot', event: HubEvent) -> tuple[discord.Embed, AppealActionView]:
    if not event.appeal_id:
        raise ValueError('Appeal ID is required to build appeal embed')

    emotes = bot.emotes

    embed = discord.Embed(
        title=f'{emotes.hammer_icon} New Appeal Submitted',
        color=discord.Color.blurple(),
        timestamp=event.timestamp,
        description=(
            'A user has submitted an appeal for a moderation action. Use the buttons below to review and decide.'
        ),
    )

    if event.moderator_id and event.moderator_name:
        embed.add_field(name='Appellant', value=f'{event.moderator_name} (`{event.moderator_id}`)', inline=True)

    embed.add_field(name='Hub', value=f'{event.hub_name} (`{event.hub_id}`)', inline=True)

    if event.reason:
        display_reason = event.reason[:1024] if len(event.reason) > 1024 else event.reason
        embed.add_field(name='Appeal Summary', value=display_reason, inline=False)

    view = AppealActionView(bot=bot, appeal_id=event.appeal_id, locale='en')

    return embed, view
