from typing import TYPE_CHECKING, Optional, Union
import discord
from sqlalchemy import select

from utils.modules.core.db.models import HubLogConfig
from utils.modules.events.eventDispatcher import HubEvent, HubEventType
from utils.constants import logger

if TYPE_CHECKING:
    from main import Bot
    from utils.modules.core.db.database import Database

MessageableChannel = Union[
    discord.TextChannel,
    discord.VoiceChannel,
    discord.StageChannel,
    discord.Thread,
    discord.DMChannel,
]


async def log_event(bot: 'Bot', event: HubEvent):
    from utils.modules.hub.loggingService import HubLoggingService

    service = HubLoggingService(bot)
    await service.log_event(event)


async def get_hub_log_config(db: 'Database', hub_id: str) -> Optional[HubLogConfig]:
    """
    Get the log configuration for a hub.
    """
    async with db.get_session() as session:
        stmt = select(HubLogConfig).where(HubLogConfig.hubId == hub_id)
        result = await session.execute(stmt)
        return result.scalar_one_or_none()


def get_log_channel_for_event(event_type: HubEventType, log_config: HubLogConfig) -> Optional[str]:
    """
    Determine which log channel to use for a specific event type.
    """

    # Map event types to log channels based on categories
    event_channel_mapping = {
        # Hub management events -> mod logs
        HubEventType.HUB_DELETE: log_config.modLogsChannelId,
        HubEventType.HUB_CREATE: log_config.modLogsChannelId,
        HubEventType.HUB_UPDATE: log_config.modLogsChannelId,
        # User moderation events -> mod logs
        HubEventType.USER_WARN: log_config.modLogsChannelId,
        HubEventType.USER_BAN: log_config.modLogsChannelId,
        HubEventType.USER_UNBAN: log_config.modLogsChannelId,
        HubEventType.USER_MUTE: log_config.modLogsChannelId,
        HubEventType.USER_UNMUTE: log_config.modLogsChannelId,
        # Server moderation events -> mod logs
        HubEventType.SERVER_WARN: log_config.modLogsChannelId,
        HubEventType.SERVER_BAN: log_config.modLogsChannelId,
        HubEventType.SERVER_UNBAN: log_config.modLogsChannelId,
        HubEventType.SERVER_MUTE: log_config.modLogsChannelId,
        HubEventType.SERVER_UNMUTE: log_config.modLogsChannelId,
        # Connection events -> join/leaves
        HubEventType.CONNECTION_ADD: log_config.joinLeavesChannelId,
        HubEventType.CONNECTION_REMOVE: log_config.joinLeavesChannelId,
        # Message events -> message moderation
        HubEventType.MESSAGE_EDIT: log_config.messageModerationChannelId,
        HubEventType.MESSAGE_DELETE: log_config.messageModerationChannelId,
        HubEventType.MESSAGE_REPORT: log_config.reportsChannelId,
        HubEventType.NSFW_DETECTED: log_config.networkAlertsChannelId,
        HubEventType.PROFANITY_VIOLATION: log_config.networkAlertsChannelId,
        # Appeals
        HubEventType.APPEAL_SUBMITTED: log_config.appealsChannelId,
    }

    return event_channel_mapping.get(event_type)


def get_log_role_for_event(event_type: HubEventType, log_config: HubLogConfig) -> Optional[str]:
    """Determine which log role to use for a specific event type."""

    # Map event types to log roles based on categories
    event_role_mapping = {
        # Hub management events -> mod logs
        HubEventType.HUB_DELETE: log_config.modLogsRoleId,
        HubEventType.HUB_CREATE: log_config.modLogsRoleId,
        HubEventType.HUB_UPDATE: log_config.modLogsRoleId,
        # User moderation events -> mod logs
        HubEventType.USER_WARN: log_config.modLogsRoleId,
        HubEventType.USER_BAN: log_config.modLogsRoleId,
        HubEventType.USER_UNBAN: log_config.modLogsRoleId,
        HubEventType.USER_MUTE: log_config.modLogsRoleId,
        HubEventType.USER_UNMUTE: log_config.modLogsRoleId,
        # Server moderation events -> mod logs
        HubEventType.SERVER_WARN: log_config.modLogsRoleId,
        HubEventType.SERVER_BAN: log_config.modLogsRoleId,
        HubEventType.SERVER_UNBAN: log_config.modLogsRoleId,
        HubEventType.SERVER_MUTE: log_config.modLogsRoleId,
        HubEventType.SERVER_UNMUTE: log_config.modLogsRoleId,
        # Connection events -> join/leaves
        HubEventType.CONNECTION_ADD: log_config.joinLeavesRoleId,
        HubEventType.CONNECTION_REMOVE: log_config.joinLeavesRoleId,
        # Message events -> message moderation
        HubEventType.MESSAGE_EDIT: log_config.messageModerationRoleId,
        HubEventType.MESSAGE_DELETE: log_config.messageModerationRoleId,
        HubEventType.MESSAGE_REPORT: log_config.reportsRoleId,
        HubEventType.NSFW_DETECTED: log_config.networkAlertsRoleId,
        HubEventType.PROFANITY_VIOLATION: log_config.networkAlertsRoleId,
        # Appeals
        HubEventType.APPEAL_SUBMITTED: log_config.appealsRoleId,
    }

    return event_role_mapping.get(event_type)


async def send_custom_log_to_hub(
    bot: 'Bot',
    hub_id: str,
    event_type: HubEventType,
    embed: discord.Embed,
    view: Optional[discord.ui.View] = None,
) -> bool:
    """Send a custom embed (and optional view) to the hub-configured log channel for the given event type."""
    log_config = await get_hub_log_config(bot.db, hub_id)
    if not log_config:
        logger.debug(f'No log configuration found for hub {hub_id}')
        return False

    channel_str = get_log_channel_for_event(event_type, log_config)
    if not channel_str:
        logger.debug(f'No log channel configured for event type {event_type.value}')
        return False

    content = None
    role_str = get_log_role_for_event(event_type, log_config)
    if role_str:
        content = f'<@&{role_str}>'

    try:
        channel_id = int(channel_str)
    except Exception:
        logger.warning(f'Invalid channel id configured for hub {hub_id}: {channel_str}')
        return False

    channel = bot.get_channel(channel_id) or await bot.fetch_channel(channel_id)
    if not isinstance(
        channel,
        (
            discord.TextChannel,
            discord.VoiceChannel,
            discord.StageChannel,
            discord.Thread,
            discord.DMChannel,
        ),
    ):
        logger.warning(f'Log channel {channel_id} is not messageable or not found')
        return False

    # Permission check for guild text channels
    if channel.guild:
        bot_member = channel.guild.me
        if bot_member:
            perms = channel.permissions_for(bot_member)
            if not perms.send_messages or not perms.embed_links:
                logger.warning(f'Bot lacks permission to send embeds in log channel {channel_id}')
                return False

    if view is not None:
        await channel.send(content, embed=embed, view=view)
    else:
        await channel.send(content, embed=embed)
    return True


async def send_staff_log(
    bot: 'Bot', embed: discord.Embed, view: Optional[discord.ui.View] = None
) -> bool:
    """Send a custom embed (and optional view) to the global InterChat staff reports channel."""
    channel_id = bot.constants.staff_report_channel_id
    if not channel_id:
        logger.warning('STAFF_REPORT_CHANNEL_ID not configured; cannot deliver report log to staff')
        return False

    channel = bot.get_channel(channel_id) or await bot.fetch_channel(channel_id)
    if not isinstance(
        channel,
        (
            discord.TextChannel,
            discord.VoiceChannel,
            discord.StageChannel,
            discord.Thread,
            discord.DMChannel,
        ),
    ):
        logger.warning(f'Staff reports channel {channel_id} is not messageable or not found')
        return False

    if view is not None:
        await channel.send(embed=embed, view=view)
    else:
        await channel.send(embed=embed)

    return True
