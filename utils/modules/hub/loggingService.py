from typing import TYPE_CHECKING, Optional
import discord

from utils.modules.events.eventDispatcher import <PERSON>bEvent, HubEventType
from utils.modules.hub.embedBuilders import (
    ModerationEmbedBuilder,
    MessageEmbedBuilder,
    DetectionEmbedBuilder,
    GenericEmbedBuilder,
)
from utils.modules.hub.hubLogging import (
    get_hub_log_config,
    get_log_channel_for_event,
    get_log_role_for_event,
)
from utils.constants import logger

if TYPE_CHECKING:
    from main import Bot


class HubLoggingService:
    def __init__(self, bot: 'Bot'):
        self.bot = bot
        self.emotes = bot.emotes

        self.moderation_builder = ModerationEmbedBuilder(self.emotes)
        self.message_builder = MessageEmbedBuilder(self.emotes)
        self.detection_builder = DetectionEmbedBuilder(self.emotes)
        self.generic_builder = GenericEmbedBuilder(self.emotes)

    async def log_event(self, event: HubEvent) -> bool:
        log_config = await get_hub_log_config(self.bot.db, event.hub_id)
        if not log_config:
            logger.debug(f'No log configuration found for hub {event.hub_id}')
            return False

        # Get channel for this event type
        channel_id = get_log_channel_for_event(event.event_type, log_config)
        if not channel_id:
            logger.debug(f'No log channel configured for event type {event.event_type.value}')
            return False

        # Create embed using appropriate builder
        embed = self.create_embed(event)
        if not embed:
            logger.warning(f'Failed to create embed for event type {event.event_type.value}')
            return False

        return await self._send_to_channel(channel_id, embed, event.event_type, log_config)

    def create_embed(self, event: HubEvent) -> Optional[discord.Embed]:
        """
        Routes to custom builders for priority events, generic builder for others.
        """
        try:
            t = event.event_type
            builder_map = {
                HubEventType.USER_BAN: self.moderation_builder.build_ban_embed,
                HubEventType.SERVER_BAN: self.moderation_builder.build_ban_embed,
                HubEventType.USER_UNBAN: self.moderation_builder.build_unban_embed,
                HubEventType.SERVER_UNBAN: self.moderation_builder.build_unban_embed,
                HubEventType.USER_MUTE: self.moderation_builder.build_mute_embed,
                HubEventType.SERVER_MUTE: self.moderation_builder.build_mute_embed,
                HubEventType.USER_UNMUTE: self.moderation_builder.build_unmute_embed,
                HubEventType.SERVER_UNMUTE: self.moderation_builder.build_unmute_embed,
                HubEventType.USER_WARN: self.moderation_builder.build_warn_embed,
                HubEventType.SERVER_WARN: self.moderation_builder.build_warn_embed,
                HubEventType.MESSAGE_DELETE: self.message_builder.build_message_delete_embed,
                HubEventType.MESSAGE_EDIT: self.message_builder.build_message_edit_embed,
                HubEventType.NSFW_DETECTED: self.detection_builder.build_nsfw_detection_embed,
                HubEventType.PROFANITY_VIOLATION: self.detection_builder.build_profanity_detection_embed,
            }
            builder = builder_map.get(t, self.generic_builder.build_generic_embed)
            return builder(event)
        except Exception as e:
            logger.error(
                f'Error creating embed for event {event.event_type.value}: {e}', exc_info=True
            )
            return None

    async def _send_to_channel(
        self, channel_id: str, embed: discord.Embed, event_type: HubEventType, log_config
    ) -> bool:
        """Send embed to the specified channel with role ping if configured"""
        try:
            channel_id_int = int(channel_id)
            channel = self.bot.get_channel(channel_id_int) or await self.bot.fetch_channel(
                channel_id_int
            )

            if not channel:
                logger.warning(f'Log channel {channel_id} not found')
                return False

            # Check if it's a messageable channel
            if not isinstance(channel, discord.abc.Messageable):
                logger.warning(f'Log channel {channel_id} is not messageable')
                return False

            # Check bot permissions
            if hasattr(channel, 'guild') and channel.guild:
                bot_member = channel.guild.me
                if bot_member is None:  # pyright: ignore[reportUnnecessaryComparison]
                    logger.warning(
                        f'Bot member not cached; cannot check perms in log channel {channel_id}'
                    )
                    return False
                bot_perms = channel.permissions_for(bot_member)
                if not bot_perms.send_messages or not bot_perms.embed_links:
                    logger.warning(
                        f'Bot lacks permission to send embeds in log channel {channel_id}'
                    )
                    return False
            # Prepare content with role ping if configured
            content = None
            allowed_mentions = discord.AllowedMentions.none()
            role_id = get_log_role_for_event(event_type, log_config)
            if role_id:
                content = f'<@&{role_id}>'
                try:
                    allowed_mentions = discord.AllowedMentions(
                        roles=[discord.Object(id=int(role_id))], users=False, everyone=False
                    )
                except (TypeError, ValueError):
                    allowed_mentions = discord.AllowedMentions.none()

            await channel.send(content=content, embed=embed, allowed_mentions=allowed_mentions)
            logger.debug(f'Logged {event_type.value} event to channel {channel_id}')
            return True

        except discord.NotFound:
            logger.warning(f'Log channel {channel_id} not found')
            return False
        except discord.Forbidden:
            logger.warning(f'Bot lacks permission to send to log channel {channel_id}')
            return False
        except discord.HTTPException as e:
            logger.warning(f'HTTP error sending to log channel {channel_id}: {e}')
            return False
        except Exception as e:
            logger.error(
                f'Unexpected error sending to log channel {channel_id}: {e}', exc_info=True
            )
            return False
