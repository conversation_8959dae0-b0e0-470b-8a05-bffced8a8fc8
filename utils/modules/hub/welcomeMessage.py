from typing import TYPE_CHECKING, Union
import discord
from sqlalchemy import select, func

from utils.constants import constants, logger
from utils.modules.core.db.models import Hub, Connection

if TYPE_CHECKING:
    from main import Bot


async def send_hub_welcome_message(
    bot: 'Bot',
    hub: Hub,
    channel: Union[discord.Thread, discord.TextChannel, discord.VoiceChannel, discord.StageChannel],
    user: discord.User | discord.Member,
) -> bool:
    # Check if hub has a welcome message configured
    if not hub.welcomeMessage or not hub.welcomeMessage.strip():
        return False

    try:
        # Get server information
        guild = channel.guild
        if not guild:
            return False

        # Get connection count for this hub
        async with bot.db.get_session() as session:
            connection_count_stmt = select(func.count(Connection.id)).where(
                Connection.hubId == hub.id
            )
            total_connections = await session.scalar(connection_count_stmt) or 0

        formatted_message = await _format_welcome_message(
            hub.welcomeMessage,
            user=user,
            hub_name=hub.name,
            server_name=guild.name,
            member_count=guild.member_count or 0,
            total_connections=total_connections,
        )

        # TODO: Localize
        embed = discord.Embed(
            title=f'Welcome to {hub.name}! 👋',
            description=formatted_message,
            color=constants.color,
        )

        if hub.iconUrl:
            embed.set_thumbnail(url=hub.iconUrl)

        embed.set_footer(
            text=f'Connected to {hub.name}',
            icon_url=bot.user.display_avatar.url if bot.user else None,
        )

        await channel.send(embed=embed)
        return True

    except Exception as e:
        try:
            # TODO: Localize
            await user.send(
                f'Failed to send welcome message for the hub you joined. Make sure I have permission to send messages in {channel.mention}.'
            )
        except Exception:
            pass

        logger.error(f'Failed to send welcome message for hub {hub.id}: {e}')
        return False


async def _format_welcome_message(
    message: str,
    user: discord.User | discord.Member,
    hub_name: str,
    server_name: str,
    member_count: int,
    total_connections: int,
) -> str:
    """
    Format a welcome message with variable substitution.

    Supported variables:
    - {user} - User mention
    - {hubName} - Hub name
    - {serverName} - Server name
    - {memberCount} - Server member count
    - {totalConnections} - Total connections to the hub
    """
    substitutions = {
        '{user}': user.mention,
        '{hubName}': hub_name,
        '{serverName}': server_name,
        '{memberCount}': str(member_count),
        '{totalConnections}': str(total_connections),
    }

    # Apply substitutions
    formatted = message
    for placeholder, value in substitutions.items():
        formatted = formatted.replace(placeholder, value)

    return formatted
