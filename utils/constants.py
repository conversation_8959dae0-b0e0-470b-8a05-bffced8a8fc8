import ast
import logging
import os
import sys

import redis.asyncio as redis_async
from dotenv import load_dotenv
from typing import Final, Dict, List

load_dotenv()


def _to_bool(value: str | None, default: bool = False) -> bool:
    if value is None:
        return default
    return str(value).strip().lower() in {'1', 'true', 'yes', 'on'}


class InterchatConstants:
    client_id: int = int(os.getenv('CLIENT_ID') or 0)  # will be set on bot startup
    production: Final[bool] = str(os.getenv('ENVIRONMENT') or 'development').lower() == 'production'
    debug: Final[bool] = _to_bool(os.getenv('DEBUG'), default=False)
    token: Final[str] = str(os.getenv('DISCORD_TOKEN') or os.getenv('TOKEN') or '')
    database_url: Final[str] = str(os.getenv('DATABASE_URL'))
    redis_uri: Final[str] = str(os.getenv('REDIS_URI'))
    prefix: Final[str] = str(os.getenv('PREFIX'))
    color: Final = 0x9172D8
    support_invite: Final[str] = str(os.getenv('SUPPORT_INVITE') or 'https://interchat.tech/support')
    donate_link: Final[str] = str(os.getenv('DONATE_LINK'))
    sentry_dsn: Final[str] = str(os.getenv('SENTRY_DSN'))
    dev_guild_id: Final[int] = int(os.getenv('DEV_GUILD_ID') or 0)
    staff_role_id: Final[int] = int(os.getenv('STAFF_ROLE_ID') or 0)
    auto_create_tables: Final[bool] = _to_bool(os.getenv('AUTO_CREATE_TABLES'), default=False)
    pool_warming: Final[bool] = _to_bool(os.getenv('POOL_WARMING'), default=False)
    sentry_send_default_pii: Final[bool] = _to_bool(os.getenv('SENTRY_PII'), default=False)
    nsfw_detector_url: Final[str] = str(os.getenv('NSFW_DETECTOR_URL') or 'http://localhost:8000/v1/detect/urls')
    enable_nsfw_detection: Final[bool] = _to_bool(os.getenv('ENABLE_NSFW'), default=True)
    staff_report_channel_id: Final[int] = int(os.getenv('STAFF_REPORT_CHANNEL_ID') or 0)
    rate_limits: Final[Dict] = {'commands': {'limit': 5, 'period': 5}, 'webhook': {'limit': 3, 'period': 300}}
    heartbeat_url: Final[str] = str(os.getenv('HEARTBEAT_URL'))

    def __init__(self):
        # frozen set of user IDs
        self.auth_users: Final[List[int]] = self._get_auth_users()

    @property
    def version(self):
        from utils.utils import parse_version_from_toml

        version: Final[str] = str(parse_version_from_toml() or '0.0.0-dev')
        if str(os.getenv('ENVIRONMENT')).lower() == 'development':
            return f'{version}-dev'

        return version

    def _get_auth_users(self) -> list[int]:
        raw = os.getenv('AUTH')
        if not raw:
            return []
        try:
            parsed = ast.literal_eval(raw)
            return [int(x) for x in parsed] if isinstance(parsed, (list, tuple)) else []
        except Exception:
            logger.warning('AUTH is not a valid list; using empty list')
            return []


constants = InterchatConstants()

redis_client = redis_async.from_url(
    constants.redis_uri,
    max_connections=100,
    health_check_interval=20,  # Seconds
    socket_connect_timeout=3,  # Seconds
    socket_timeout=5,  # Seconds
    retry_on_timeout=True,
    decode_responses=True,
)

if not constants.production:
    from rich.logging import RichHandler
    from rich.theme import Theme
    from rich.console import Console

    console = Console(
        theme=Theme(
            {
                'logging.level.info': '#a6e3a1',
                'logging.level.debug': '#8aadf4',
                'logging.level.warning': '#f9e2af',
                'logging.level.error': '#f38ba8',
            }
        )
    )
    handler = RichHandler(tracebacks_width=200, console=console)
else:
    handler = logging.StreamHandler()  # plain logs for prod


handler.setFormatter(logging.Formatter('%(name)s: %(message)s'))
level = logging.DEBUG if constants.debug else logging.INFO

logger = logging.getLogger('InterChat')
logger.setLevel(level)
logger.addHandler(handler)
logger.propagate = False

watch_log = logging.getLogger('cogwatch')
watch_log.setLevel(level)
watch_log.addHandler(handler)
watch_log.propagate = False

discord_logger = logging.getLogger('discord')
discord_logger.setLevel(logging.INFO)
discord_logger.addHandler(handler)
discord_logger.propagate = False

# discord_http_logger = logging.getLogger('discord.http')
# discord_http_logger.setLevel(logging.INFO)
# discord_http_logger.addHandler(handler)
# discord_http_logger.propagate = False


def handle_exception(exc_type, exc_value, exc_traceback):
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    # Log the exception with full traceback
    logger.critical('Uncaught exception', exc_info=(exc_type, exc_value, exc_traceback))
