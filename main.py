import asyncio
import os
import sys
import time
from datetime import datetime

import aiohttp
import discord
from cogwatch import watch
from discord.ext import commands
from sqlalchemy import select

from utils.modules.core.uptime import pingServerLoop
from utils.constants import constants, handle_exception, logger
from utils.modules.core.db.database import Database, init_database
from utils.modules.core.db.models import (
    Appeal,
    AppealStatus,
    DevAlerts,
    HubReport,
    ReportStatus,
    ServerData,
    User,
)
from utils.modules.core.db.redis import validate_redis_connection, warm_redis_pool
from utils.modules.core.rateLimit import message_rate_limit
from utils.modules.emojis.EmojiManager import EmojiManager
from utils.modules.moderation.ui.views import ReportActionView, AppealActionView

if constants.production:
    import sentry_sdk

    sentry_sdk.init(
        dsn=constants.sentry_dsn,
        traces_sample_rate=1.0,
        enable_logs=True,
        profiles_sample_rate=1.0,
        send_default_pii=constants.sentry_send_default_pii,
        release=f'interchat@{constants.version}',
    )

    presence = 'Building bridges across your servers'

else:
    sys.excepthook = handle_exception
    presence = 'In development'


class Bot(commands.AutoShardedBot):
    # Suppress error on the User attribute being None since it fills up later
    user: discord.ClientUser  # pyright: ignore[reportIncompatibleMethodOverride]

    def __init__(self):
        intent = discord.Intents.default()
        intent.message_content = True
        intent.members = True
        intent.guilds = True

        super().__init__(
            command_prefix=constants.prefix,
            intents=intent,
            chunk_guilds_at_startup=False,
            help_command=None,
            owner_ids=constants.auth_users,
            case_insensitive=True,
            strip_after_prefix=True,
            allowed_mentions=discord.AllowedMentions(everyone=False, users=True, roles=False, replied_user=True),
        )

        self.start_time = datetime.now()
        self.before_invoke(self.before_commands)
        self.after_invoke(self.after_commands)
        self.guilds_chunked = asyncio.Event()
        self.db: Database
        self.uptime = 0
        self.emotes = EmojiManager()
        self.staff_ids: list[int] = []  # loaded from dev guild during start up
        self.http_session: aiohttp.ClientSession = None  # pyright: ignore[reportAttributeAccessIssue]
        self.constants = constants

    async def before_commands(self, ctx: commands.Context['Bot']):
        await bot.wait_until_ready()
        if ctx.guild and not ctx.guild.chunked:
            await ctx.guild.chunk(cache=True)

        await message_rate_limit(ctx)

    async def after_commands(self, ctx):
        if not ctx.guild:
            return
        async with self.db.get_session() as session:
            stmt = select(ServerData.id).where(ServerData.id == str(ctx.guild.id))
            existing = (await session.execute(stmt)).scalar()

            if not existing:
                server = ServerData(
                    id=str(ctx.guild.id),
                    name=ctx.guild.name,
                    createdAt=datetime.now(),
                    updatedAt=datetime.now(),
                    iconUrl=ctx.guild.icon.url if ctx.guild.icon else None,
                    lastMessageAt=datetime.now(),
                    inviteCode='abcd',
                )
                session.add(server)
                await session.commit()
                logger.debug(f'Created new server: {server.name}')

                userstmt = select(User.inboxLastReadDate).where(User.id == str(ctx.author.id))
                lastRead = await session.scalar(userstmt)

                if lastRead is not None:
                    devstmt = select(DevAlerts).where(DevAlerts.createdAt > lastRead)
                    alerts = (await session.execute(devstmt)).scalars().all()
                else:
                    alerts = []

                if alerts:
                    embed = discord.Embed(
                        title='New alert!',
                        description='Our Team has sent an alert, check it out with the `/inbox` command!',
                        color=self.constants.color,
                    )
                    await ctx.reply(content=ctx.author.mention, embed=embed)

    async def is_owner(self, user: discord.abc.User) -> bool:
        if user.id in constants.auth_users:
            return True
        return False

    async def setup_hook(self):
        # Initialize database connection
        try:
            self.db = init_database(constants.database_url)
            if constants.auto_create_tables:
                await self.db.create_tables()

            if self.http_session is None or self.http_session.closed:  # pyright: ignore[reportUnnecessaryComparison]
                self.http_session = aiohttp.ClientSession(
                    connector=aiohttp.TCPConnector(
                        limit=300,
                        limit_per_host=100,
                        keepalive_timeout=30,
                        enable_cleanup_closed=True,
                    ),
                    timeout=aiohttp.ClientTimeout(total=10),
                )

            logger.info('Database initialized successfully')
        except Exception as e:
            logger.error(f'Failed to initialize database: {e}')
            raise

        await validate_redis_connection()
        if constants.pool_warming:
            await warm_redis_pool()

        count = 0
        loaded_cogs = []
        for root, _, files in os.walk('./cogs'):
            for file in files:
                if file.endswith('.py'):
                    cog_path = os.path.relpath(os.path.join(root, file), './cogs')
                    cog_module = cog_path.replace(os.sep, '.')[:-3]

                    try:
                        await bot.load_extension(f'cogs.{cog_module}')
                        count += 1
                        loaded_cogs.append(cog_module)
                    except Exception as e:
                        logger.error(f'{cog_module} failed to load: {e}')

        logger.info(f'Successfully loaded {count} cog(s). Loaded: {", ".join(loaded_cogs)}')
        await bot.change_presence(activity=discord.CustomActivity(name=presence))

    async def on_connect(self):
        logger.info('Connected to discord gateway')

    async def on_disconnect(self):
        logger.warning('Disconnected from discord gateway')

    async def on_shard_connect(self, shard_id: int):
        await self.tree.sync()
        logger.info(f'Shard {shard_id} has connected to discord gateway')

    async def on_shard_disconnected(self, shard_id: int):
        logger.info(f'Shard {shard_id} has disconnected from discord gateway')

    @watch(path='cogs', preload=False, default_logger=False, colors=False)
    async def on_ready(self):
        # await self.tree.sync() # This broke a lot, not sure why... need to investigate
        bot.uptime = int(time.time())
        await bot.change_presence(activity=discord.CustomActivity(name=presence))

        logger.debug(f'{self.user} has logged in.')
        self.constants.client_id = self.user.id if self.user else self.constants.client_id

        await self.emotes.load(self)
        await self.sync_staff_ids()
        logger.info(f'{self.user} has started successfully.')

        async with self.db.get_session() as session:
            stmt = select(HubReport.id).where(HubReport.status == ReportStatus.PENDING)
            report_ids = (await session.execute(stmt)).scalars().all()
            for report_id in report_ids:
                report_view = ReportActionView(bot=self, report_id=report_id)
                self.add_view(report_view)

            # Register persistent appeal action views for pending appeals
            stmt2 = select(Appeal.id).where(Appeal.status == AppealStatus.PENDING)
            appeal_ids = (await session.execute(stmt2)).scalars().all()
            for appeal_id in appeal_ids:
                appeal_view = AppealActionView(bot=self, appeal_id=appeal_id)
                self.add_view(appeal_view)

    async def close(self):
        try:
            await self.db.dispose()
            if self.http_session and not self.http_session.closed:
                await self.http_session.close()
        finally:
            await super().close()

    async def sync_staff_ids(self):
        dev_guild = self.get_guild(constants.dev_guild_id)

        if dev_guild:
            staff_role = dev_guild.get_role(constants.staff_role_id)

            # chunk it
            await dev_guild.chunk()

            if staff_role:
                # remove all existing ids
                self.staff_ids.clear()
                for member in staff_role.members:
                    self.staff_ids.append(member.id)
                logger.debug(f'Loaded {len(self.staff_ids)} staff IDs from dev guild.')
            else:
                logger.warning('Staff role not found in dev guild.')
        else:
            logger.warning('Dev guild not found. Staff commands may fail to work.')


bot = Bot()


async def start_bot():
    max_retries = 10
    retry_delay = 5
    retries = 0

    while retries < max_retries:
        try:
            logger.info(f'Starting bot... (Attempt {retries + 1})')
            async with bot:
                await bot.start(constants.token)
        except discord.LoginFailure:
            logger.critical('Invalid bot token provided')
            sys.exit('FAILED TO START: INVALID TOKEN')
        except discord.HTTPException as e:
            logger.error(f'HTTP Exception occurred: {e}')
        except discord.GatewayNotFound:
            logger.error('Discord gateway could not be found')
        except (OSError, TimeoutError, ConnectionResetError) as e:
            logger.error(f'Connection error occurred: {e}')
        except Exception as e:
            logger.critical(f'Unexpected error occurred: {e}')
            sys.exit('FAILED TO START: UNEXPECTED ERROR')

        if retries < max_retries:
            retries += 1
            logger.info(f'Retrying in {retry_delay} seconds...')
            await asyncio.sleep(retry_delay)
        else:
            break


async def runtime():
    bot = asyncio.create_task(start_bot())
    uptime = asyncio.create_task(pingServerLoop())
    await asyncio.gather(bot, uptime)


if __name__ == '__main__':
    try:
        asyncio.run(runtime())
    except KeyboardInterrupt:
        logger.warning('Bot shutting down...')
        sys.exit(0)
