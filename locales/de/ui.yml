ui:
  setup:
    embed:
      description: "Verwen<PERSON> das Modal, um deine Hub-Details einzugeben."
    select:
      placeholder: "Sprache auswählen"
      loadingLabel: "Lädt..."
      loadingDescription: "Bitte warten..."
      chooseOption: "Wähle eine Option, um zu beginnen..."
    buttons:
      back: "Zurück"
      cancel: "Abbrechen"
      refresh: "Aktualisieren"
      enterHubInfo: "Hub-Informationen eingeben"
      hubInfoComplete: "Hub-Informationen abgeschlossen"
      createHub: "Hub erstellen"
      completeInfoFirst: "Vervollständige zuerst die Hub-Informationen"
      discoverHubs: "Öffentliche Hubs entdecken"
      supportServer: "Support-Server"
  common:
    titles:
      error: "Fehler!"
      success: "Erfolg!"
      cancelled: 'Abgebrochen!'
    noDescription: "Keine Beschreibung"
    labels:
      date: "Datum"
      create: 'Erstellen'
      cancel: 'Abbrechen'
    messages:
      loading: "Lädt..."
      pleaseWait: "Bitte warten..."
      notImplemented: "Noch nicht implementiert."
      hubNotFound: "Hub nicht gefunden. Bitte später erneut versuchen."
      serverNotFound: "Server nicht gefunden."
      userNotFound: "Benutzer nicht gefunden."
      notSpecified: "Nicht angegeben"
      interfacePermission: "Du kannst diese Oberfläche nicht benutzen."
      hubUpdateFailed: "Aktualisierung der Hub-Einstellungen fehlgeschlagen."
      hubConfigDescription: "Passe deinen InterChat Hub für deine Community an. Wähle unten aus, was du konfigurieren möchtest."
    pagination:
      previous: "Zurück"
      next: "Weiter"
      page: "Seite (aktuell)/(gesamt)"
    errors:
      notYourMenu: "Das ist nicht dein Menü."
      cannotControlPagination: "Du kannst diese Seitennummerierung nicht steuern."
      notFound: "Nicht gefunden"
      invalidFilter: "Ungültiger Filter"
    modal:
      hubCreation:
        title: "Erstelle deinen Hub"
        name:
          label: "Hub-Name"
          placeholder: "Gib einen eindeutigen Namen für deinen Hub ein"
        brief:
          label: "Kurze Beschreibung"
          placeholder: "Eine kurze Zusammenfassung des Zwecks deines Hubs"
        description:
          label: "Detaillierte Beschreibung"
          placeholder: "Erkläre den Leuten, worum es in deinem Hub geht und was sie erwarten können"
        logoUrl:
          label: "Hub-Logo-URL"
          placeholder: "https://beispiel.de/dein-logo.png"
    create:
      footer:
        getStarted: "Klicke unten auf „Hub-Informationen eingeben“, um zu starten!"
  help:
    select:
      placeholder: "Wähle eine Kategorie"
  preferences:
    title: "Benutzereinstellungen"
    description: "Bearbeite deine persönlichen Einstellungen, damit InterChat für dich funktioniert. Diese sind GLOBAL und gelten für alle Server, die du mit InterChat teilst."
    buttons:
      return: "Zurück"
    replyMentions:
      title: "Preferences: Reply Mentions"
      description: "Du wirst nun {status}, wenn jemand auf deine Nachrichten antwortet.\nDu kannst dies unten über den Button umschalten oder mit dem Zurück-Button zum Menü zurückkehren.\nui.preferences.replyMentions.description\n"
      currentDescription: "Derzeit bist du \"{status}\", wenn innerhalb deiner Nachrichten geantwortet wird. Du kannst dies unten mit dem Schalter ändern oder mit der Zurück-Schaltfläche ins Menü zurückkehren.\nui.preferences.replyMentions.currentDescription"
      mentioned: "erwähnt"
      notMentioned: "Nicht erwähnt"
    errors:
      noPreferences: "Sie haben noch keine Einstellung eingerichtet."
      hide: "Verstecken"
    general:
      title: "Allgemeine Einstellungen"
      description: "Konfigurieren Sie allgemeine Einstellungen in InterChat. So stellen wir sicher, dass Sie das bestmögliche Erlebnis haben."
    locale:
      title: "Sprachauswahl"
      description: "Wählen Sie Ihre bevorzugte Sprache für InterChat."
    select:
      placeholder: "Wählen Sie eine Option"
      category: "Wähle eine Kategorie"
    badges:
      title: "Einstellungen: Abzeichen"
      description: "Ihre Abzeichen befinden sich jetzt in Ihren Nachrichten im Status **{status}**. Sie können dies unten mit der Schaltfläche umschalten oder mit der Schaltfläche „Zurück“ zum Menü zurückkehren."
      currentDescription: "Derzeit befinden sich Ihre Badges in Ihren Nachrichten im Status **{status}**. Sie können dies unten mit der Schaltfläche umschalten oder mit der Schaltfläche „Zurück“ zum Menü zurückkehren."
      visible: "sichtbar"
      hidden: "versteckt"
      buttons:
        show: "Zeigen"
        hide: "Verstecken"
  appeal:
    buttons:
      previous: "Zurück"
      next: "Weiter"
    select:
      placeholder: "Wählen Sie einen Verstoß aus, gegen den Sie Einspruch einlegen möchten"
    errors:
      cannotControl: "Sie können dieses Paginierungsmenü nicht steuern."
      notYourMenu: "Dies ist nicht Ihr Berufungsmenü."
      nothingSelected: "Es wurde nichts ausgewählt."
      invalidSelection: "Ungültige Auswahl."
      modalError: "Das Öffnen des Einspruchsmodals ist fehlgeschlagen."
    modal:
      title: "Einspruch einreichen"
      q1: "Warum glauben Sie, dass diese Aktion falsch war?"
      q2: "Erzählen Sie uns von der Situation"
      q3: "Weitere Informationen"
    success:
      submitted: "{tick} Ihr Einspruch wurde zur Prüfung eingereicht."
    viewInfractions:
      title: "Aktuelle Benutzerverstöße"
      empty: "Für diesen Benutzer wurden keine Verstöße gefunden."
    status:
      acceptedBy: "Akzeptiert von @{name}"
      rejectedBy: "Abgelehnt von @{name}"
    actions:
      decisionTitle: "Berufungsentscheidung"
      reasonOptional:
        label: "Grund (optional)"
        placeholder: "Begründen Sie Ihre Entscheidung..."
  report:
    buttons:
      toStaff: "Bericht an die Mitarbeiter"
      toHub: "Bericht an Hub"
    modal:
      toStaff:
        title: "Melden Sie sich beim InterChat-Personal"
      toHub:
        title: "Den Hub-Moderatoren melden"
      reason:
        label: "Grund für die Meldung"
        placeholder: "Bitte beschreiben Sie, warum Sie diese Nachricht melden ..."
    status:
      resolvedBy: "Gelöst von {name}"
      ignoredBy: "Ignoriert von {name}"
  hubConfig:
    main:
      placeholder: "Wählen Sie eine Option"
      loadingLabel: "Lädt..."
      loadingDescription: ""
    general:
      placeholder: "Choose a setting to configure..."
      editDescription:
        label: "Edit Description"
        description: "Update your hub's description text"
      editName:
        label: "Edit Hub Name"
        description: "Change your hub's name (10 day cooldown)"
      welcomeMessage:
        label: "Welcome Message"
        description: "Set a message shown to new users"
      toggleNsfw:
        label: "Toggle NSFW"
        description: "Mark your hub as NSFW or not"
      togglePrivate:
        label: "Toggle Private"
        description: "Mark your hub as private or not"
    permissions:
      remove:
        label: "Remove"
      moderator:
        label: "Moderator"
        description: "Manage messages, moderate users, handle reports & appeals"
      manager:
        label: "Manager"
        description: "Manage hub settings plus everything that moderators can do"
      managerOnly:
        description: "Manage hub settings"
    modules:
      reactions:
        description: "Allow reaction-based interactions on messages"
      hideLinks:
        description: "Hide link previews in hub messages"
      spamFilter:
        description: "Automatically filter spam and unwanted content"
      blockInvites:
        description: "Block Discord server invites in messages"
      useNicknames:
        description: "Display user nicknames instead of usernames"
    logging:
      moderationLogs: "Moderation Logs"
      joinLeaveLogs: "Join/Leave Logs"
      appealsChannel: "Appeals Channel"
      reportsChannel: "Reports Channel"
      networkAlerts: "Network Alerts"
      messageModeration: "Message Moderation"
    invites:
      create:
        title: "Input Required"
        customCode:
          label: "Custom Code"
          placeholder: "abc123"
        uses:
          label: "Uses"
          placeholder: "Leave me blank for infinite"
        expiry:
          label: "Expiry"
          placeholder: "1 week"
      buttons:
        create: "Erstellen"
  moderation:
    actionNames:
      warned: "Warned"
      muted: "Muted"
      banned: "Banned"
    actionNouns:
      mute: "mute"
      ban: "ban"
    prep:
      in: "in"
      from: "from"
    actions:
      warn:
        label: "Warn"
        description: "Issue a warning to the selected target"
      mute:
        label: "Mute"
        description: "Mute a user/server from the specified hub"
      ban:
        label: "Ban"
        description: "Ban a user/server from the specified hub"
      unmute:
        label: "Unmute"
        description: "Revoke an active mute in this hub"
      unban:
        label: "Unban"
        description: "Revoke an active ban in this hub"
      blacklist:
        label: "Blacklist"
        description: "Issue an interchat wide blacklist (user/server)"
      delete:
        label: "Delete"
        description: "Delete the selected message across all connected hubs"
    modal:
      title: "Moderation Action"
      reason:
        label: "Reason"
        placeholder: "Enter a concise justification{optional}"
      duration:
        label: "Duration"
        placeholder: "e.g. 30m, 2h, 1d"
    hubSelect:
      placeholder: "Select a hub to moderate..."
    hubSelection:
      title: "Hub Selection"
      description: "Select the hub you want to moderate."
      fieldHubLabel: "Hub"
      fieldHubPrompt: "Selected Hub"
      prompt: "Select a hub to moderate"
    targetSelection:
      title: "Target Selection"
      description: "Who do you want to {action}?"
      userField: "User"
      serverField: "Server"
    actionSelect:
      placeholder: "Select a moderation action..."
    fields:
      reason: "Reason"
  infractions:
    buttons:
      userInfractions: "User Infractions"
      serverInfractions: "Server Infractions"
    titles:
      base: "Infractions · {hubName}"
      userList: "User Infractions · {hubName}"
      serverList: "Server Infractions · {hubName}"
      userSpecific: "Infractions for {user} · {hubName}"
      serverSpecific: "Infractions for Server {serverId} · {hubName}"
    descriptions:
      base: "Select which infractions to view for this hub."
      userListEmpty: "No user infractions found for this hub."
      serverListEmpty: "No server infractions found for this hub."
      userSpecificEmpty: "No infractions found for this user in this hub."
      serverSpecificEmpty: "No infractions found for this server in this hub."
    labels:
      infraction: "Infraction"
    fields:
      userName: "User Name"
      userId: "User ID"
      serverName: "Server Name"
      serverId: "Server ID"
      reason: "Reason"
      moderator: "Moderator"
      issued: "Issued"
      status: "Status"
  staff:
    hub:
      section:
        status: "Status"
        moderation: "Moderation"
      fields:
        name: "Name"
        messages: "Messages"
        connections: "Connections"
        created: "Created"
        lastActive: "Last Active"
        upvotes: "Upvotes"
        owner: "Owner"
        location: "Location"
        activity: "Activity"
        appealCooldown: "Appeal Cooldown"
        welcomeMessage: "Welcome Message"
        description: "Description"
      values:
        messagesThisWeek: "**{count}** this week"
        connectionsActive: "**{count}** active"
        upvotesTotal: "**{count}** total"
      badges:
        verified: "Verified"
        partnered: "Partnered"
        featured: "Featured"
        private: "Private"
        locked: "Locked"
        nsfw: "NSFW"
    server:
      fields:
        name: "Name"
        serverId: "Server ID"
        inviteCode: "Invite Code"
        messages: "Messages"
        connections: "Connections"
        status: "Status"
        created: "Created"
        lastMessage: "Last Message"
        updated: "Updated"
      values:
        messagesTotal: "**{count}** total"
        connectionsActive: "**{count}** active"
        premium: "Premium"
        standard: "Standard"
    user:
      fields:
        name: "Name"
        userId: "User ID"
        status: "Status"
        messages: "Messages"
        reputation: "Reputation"
        votes: "Votes"
        hubJoins: "Hub Joins"
        engagement: "Engagement"
        locale: "Locale"
        created: "Created"
        lastMessage: "Last Message"
        lastVote: "Last Vote"
      values:
        staff: "Staff"
        member: "Member"
        messagesSent: "**{count}** sent"
        reputationPoints: "**{count}** points"
        votesCast: "**{count}** cast"
        hubJoinsTotal: "**{count}** total"
        notSet: "Not set"
        never: "Never"
      sections:
        preferences: "Preferences"
        createdContent: "Created Content"
        activity: "Activity"
        donation: "Donation"
        badges: "Badges"
      preferences:
        showBadges: "Show badges"
        mentionOnReply: "Mention on reply"
        showNsfwHubs: "Show NSFW hubs"
      content:
        hubs: "{count} hubs"
        modPositions: "{count} mod positions"
        blockedWords: "{count} blocked words"
        antiSwearRules: "{count} anti-swear rules"
      moderation:
        infractionsReceived: "{count} infractions received"
        infractionsIssued: "{count} infractions issued"
        blacklistsReceived: "{count} blacklists received"
        blacklistsIssued: "{count} blacklists issued"
      activity:
        appeals: "{count} appeals"
        reviews: "{count} reviews"
        reportsMade: "{count} reports made"
        reportsReceived: "{count} reports received"
        achievements: "{count} achievements"
      donation:
        tier: "Tier: {tier}"
        expires: "Expires"
    blacklist:
      titles:
        list: "Blacklist Entries"
        searchResults: "Blacklist Search Results"
        userList: "User Blacklist"
        serverList: "Server Blacklist"
      descriptions:
        list: "Total blacklist entries"
        noEntriesPage: "No entries on this page."
        userListEmpty: "No blacklisted users found."
        serverListEmpty: "No blacklisted servers found."
      fields:
        user: "User"
        server: "Server"
        expires: "Expires"
        staff: "Staff"
        added: "Added"
      labels:
        user: "User"
        server: "Server"
  hub:
    title: 'Hub Creation'
    description: 'Create your own InterChat hub for a community, your friends... or anyone - the list could go on for ages. Go ahead, see where it takes you.'
    delete:
      description: 'Your hub has been deleted.'
    connect:
      success:
        title: 'Connected!'
        description: 'Connected to **{hubName}** - get chatting!'
        fieldValue: 'Connected Channel: {channel}'
      errors:
        alreadyConnected: 'Uh oh! It looks like this server already has a connection to this hub.'
    disconnect:
      title: 'Disconnected!'
      description: 'Disconnected from hub.'
      fieldValue: 'Associated Channel: {channel}'
    invites:
      title: 'Active Invites'
      inviteUses: '**Uses:**'
      inviteExpire: '**Expires:**'
      noneFound: 'None found!'
    announcements:
      title: 'Hub Announcements'
      description: "Create a **one time** announcement that sends in all connected channels. \n\n-# To create **recurring** announcements please visit the hub configuration menu."
    creation:
      title: "Let's set up your hub!"
      description: "Hubs link multiple servers together using webhooks. They're great for cross-server communities."
      clickToBegin: "Click {emoji} Create to begin."
      modal:
        title: 'Hub Information'
        hubName: "Hub name"
        shortLabel: "Short description"
      errors:
        invalidInput: 'Invalid Input'
        name: 'Uh oh! Your hub name must be composed of letters, numbers, spaces and underscores. We do not allow any other characters.'
        shortDescription: "The hub's short description must be between 10 and 100 characters."
        unhandled: 'Whoops! Something went wrong while attempting to create your hub. Please try again, and if the issue persists contact our (support team)[supportServer].'
        unique: 'Uh oh! That hub name has already been taken. You must choose another.'
      visibility:
        title: 'Hub Visibility'
        description: 'Should your hub **public**? This means it will be accessible to **everyone** and show on the [discovery page]({discoverHubs}).'
        buttons:
          public: 'Public'
          private: 'Private'
      preview:
        title: 'Complete!'
        description: 'You have complete the hub setup! Review your choices below to contiune.'
        footer: 'You may edit more fields later, after creation.'
        fields:
          title: 'Information'
          value: |
            > {nemoji} **Name:** {hubName}
            > {sdemoji} **Short description:** {shortDescription}
            > {vemoji} **Visibility:** {visibility}
            > {ldemoji} **Long description:** None - Set this in the [dashboard]({dashboard}) later!
      complete:
        title: 'Created!'
        description: "Your hub is now ready - get chatting! Looking for more customisation? Head over to the [dashboard]({dashboard}) and configure your hub's settings."
        private: '**Invite Users!** As your hub is private you must generate user invites. Create one with `/hub invites`!'
        public: '**Your creation!** As your hub is public, you may view it on the explorer - [check it out]({public})!'
      other:
        cancelled: 'Setup cancelled. We hope to see you back here again soon.'
    config:
      title: "Hub Configuration"
      description: "Passe deinen InterChat Hub für deine Community an. Wähle unten aus, was du konfigurieren möchtest."
      options:
        general:
          label: "General Settings"
          description: "Customize your hub's appearance, rules, and more"
        rules:
          label: "Hub Rules"
          description: "Manage your hub's community rules and guidelines"
        team:
          label: "Team Management"
          description: "Manage your hub's team and staff permissions"
        modules:
          label: "Modules"
          description: "Enable or disable additional modules within your hub"
        logging:
          label: "Logging Configuration"
          description: "Configure logging channels and notification roles for your hub"
        transfer:
          label: "Transfer Ownership"
          description: "Transfer your hub's ownership to another person"
        announcements:
          label: "Scheduled Announcements"
          description: "Configure automatic scheduled announcements for your hub"
    all:
      title: "InterChat Hubs"
      description: "Below you will be able search through a list of all our hubs! Check them out, see who you find."
      filteredBy: 'Results filtered by: {filter}'
      errors:
        notFound: "Uh oh! I was unable to find any hubs. That's probably not supposed to happen. Please contact our [support team]({supportServer})."
        invalidFilter: "Crickets... no hubs match your filter."
      modal:
        title: 'Search Filter'
        searchFilter:
          label: 'Filter'
          placeholder: 'InterChat Central'
    rules:
      title: "Hub Rules Management"
      description: "Manage your hub's community rules and guidelines below."
      noRules: "No rules have been set for this hub yet."
      selectRule:
        placeholder: "Select a rule to edit or delete..."
      addRule:
        label: "Add Rule"
        modal:
          title: "Add New Rule"
          placeholder: "Enter your new rule here..."
      editRule:
        label: "Edit Rule"
        modal:
          title: "Edit Rule"
          placeholder: "Enter your updated rule here..."
      deleteRule:
        label: "Delete Rule"
        confirmation: "Are you sure you want to delete this rule?"
      moveUp:
        label: "Move Up"
      moveDown:
        label: "Move Down"
      viewAll:
        label: "View All Rules"
      validation:
        empty: "Rule cannot be empty."
        tooLong: "Rule must be {maxLength} characters or less."
        maxRules: "Maximum of {maxRules} rules allowed."
      success:
        added: "Rule added successfully!"
        updated: "Rule updated successfully!"
        deleted: "Rule deleted successfully!"
        moved: "Rule moved successfully!"
      errors:
        maxRulesReached: "Max Rules Reached ({maxRules})"
        noRulesAvailable: "No rules available to select"
        sessionExpired: "Session Expired"
        sessionExpiredDescription: "This configuration session has expired. Please run the command again to continue managing rules."
        actionCancelled: "Action Cancelled"
        actionCancelledDescription: "Rule action has been cancelled. No changes were made."
        ruleNotFound: "Invalid rule selection. The rule may have been deleted by another user."
        invalidRuleSelection: "Invalid rule selection. Please try again."
        processingError: "An error occurred while processing your request. Please try again."
        refreshError: "An error occurred while refreshing. Please try again."
        navigationError: "An error occurred while navigating. Please try again."
        selectionError: "An error occurred while selecting the rule. Please try again."
        deletionCancelled: "Cancelled"
        deletionCancelledDescription: "Rule deletion has been cancelled. No changes were made."
      warnings:
        deleteWarning: "Warning"
        deleteWarningDescription: "This action cannot be undone. The rule will be permanently removed from your hub."
      display:
        noRulesTitle: "No Rules Set"
        noRulesDescription: "No rules have been set for this hub yet. Use the \"Add Rule\" button below to create your first rule."
        currentRulesTitle: "Current Rules ({current}/{max})"
        ruleSelected: "Rule {number} Selected"
        currentRule: "Current Rule:"
  connection:
    fixAll:
      label: "Fix All Connections"
    manage:
      placeholder: "Select a connection to manage"
