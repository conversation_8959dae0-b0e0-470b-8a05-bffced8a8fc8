commands:
  about:
    title: "A propose de InterChat"
    description_text: "."
    support_text: "Besoin d'aide ? Rejoignez notre serveur d'assistance !"
    features:
      title: "Caractéristiques"
      list: |
        - Connectez-vous à d'autres serveurs pour des discussions actives entre serveurs
        - Les messages circulent naturellement entre les serveurs en temps réel
        - Créez des communautés engagées et axées sur un sujet
        - Outils de modération pour des discussions dynamiques
        - Tableau de bord visuel pour gérer vos hubs, serveurs et paramètres
    buttons:
      vote: "Voter sur top.gg"
      invite: "Inviter InterChat"
      dashboard: "Ouvrez les tableaux de bord"
      support: "Rejoignez le support"
      shardInfo: "Info partagée"
  setup:
    welcome:
      title: "Bienvenue dans la configuration d'InterChat"
      description: "Connectez votre serveur. Cet assistant vous guidera pour créer ou rejoindre un hub."
    options:
      create:
        label: "Crée un Hub"
        description: "Commencer votre communauté InterChat"
      join:
        label: "Rejoignez un Hub"
        description: "Se connecter à une communauté existante"
    create:
      whatYoullCreate:
        title: "Ce que vous allez créer :"
        description: "{dot} Un espace communautaire unique pour vos serveurs {dot} Contrôle total des règles et de la modération {dot} Paramètres et fonctionnalités personnalisés {dot} Privé par défaut - sur invitation uniquement"
      youllNeed:
        title: "Vous aurez besoin de :"
        description: "{arrow_right} Nom du hub créatif {arrow_right} Brève description {arrow_right} Description détaillée {arrow_right} URL de l'image du logo"
    join:
      title: "Rejoignez un Hub"
      description: "Utilisez l'annuaire public ou connectez-vous avec un code d'invitation."
      publicHubs:
        title: "Hubs publics"
        description: "**Parcourir** [interchat.tech/hubs](https://interchat.tech/hubs) **Ou cliquez** sur le bouton ci-dessous pour ouvrir le répertoire"
      privateHubs:
        title: "Privée ou invitations uniquement"
        description: "**Demandez au propriétaire ou au gestionnaire du hub** un code d'invitation **Exécutez `/connect <invite-code>`** sur votre serveur pour rejoindre"
      footer: "Vous pouvez rejoindre plusieurs hubs à tout moment et vous pouvez être connecté à plusieurs hubs à la fois dans différents canaux."
    nextSteps:
      created:
        title: "Hubs crée avec succès!"
        description: |
          Votre hub **{hubName}** est prêt ! Voici ce que vous pouvez faire ensuite : - Inviter votre premier serveur avec `/invite` - Définir des règles avec `/rules set` - Personnaliser les paramètres dans le [tableau de bord](https://interchat.tech/dashboard) - Connecter des canaux avec `/connect <hub-name>`
        inviteLink:
          title: "Suivant : Créer des liens d’invitation"
          description: "Utilisez {hubInviteCommand} pour générer des codes d'invitation pour **{hubName}** et les partager avec d'autres propriétaires de serveurs."
        shareHub:
          title: "Partager votre hub"
          description: "{dot} Publiez des informations sur votre nouveau hub sur notre [Serveur d'assistance]({supportInvite}) {dot} Partagez avec vos amis et vos communautés {dot} Utilisez les réseaux sociaux pour faire passer le message"
        proTips:
          title: "{dot} conseil de pro"
          description: "{dot} Visitez le [Tableau de bord]({website}) pour les paramètres avancés {dot} Utilisez {hubVisibilityCommand} pour rendre votre hub public {dot} Rejoignez notre [Serveur d'assistance]({supportInvite}) pour obtenir de l'aide et des mises à jour"
        footer: "Besoin d'aide ? Rejoignez notre serveur support."
  report:
    title: "Signaler le message"
    footer: "Les rapports contribuent à assurer la sécurité d'InterChat pour tous"
    contextMenu: "Signaler le message"
    description: "Signalez {user} à l'équipe du Hub ou à l'équipe d'InterChat pour toute violation de la plateforme. L'utilisateur ne sera pas averti, mais les modérateurs pourront voir qui a soumis le signalement."
    success:
      title: "Signalement envoyé"
      toStaff: "{tick} Votre rapport a été envoyé au personnel d'InterChat pour examen."
      toHub: "{tick} Vous ne pouvez signaler que les messages envoyés via les hubs InterChat."
    errors:
      hubMessageOnly: "{x_icon} Vous ne pouvez signaler que les messages envoyés via les hubs InterChat."
      cannotReportSelf: "{x_icon} Vous ne pouvez pas signaler vos propres messages."
  hubCreate:
    success:
      description: "Votre hub **{hubName}** est prêt ! Voici ce que vous pouvez faire ensuite :"
    errors:
      hubCreationFailed: "Une erreur s'est produite lors de la création de votre hub. Veuillez réessayer."
      troubleshooting: "Ce que vous pouvez faire :"
      troubleshootingSteps: "• Vérifiez que l'URL de votre logo est valide • Réessayez dans quelques instants • Contactez le support si cela persiste"
  general:
    invite:
      title: "Hey!"
      description: "Merci d'avoir choisi InterChat. Besoin d'aide ? Rejoignez notre serveur d'assistance. Utilisez les boutons ci-dessous pour inviter notre bot sur votre serveur et commencer à vous connecter."
  stats:
    title: "Métriques InterChat"
    shard:
      title: "Informations sur les éclats"
      statusReady: "Prêt"
      statusProvisioning: "Approvisionnement..."
      current: "Éclat actuel #{id}"
  help:
    title: "InterChat commandes"
    description: "Explorez notre large gamme de commandes pour vous aider, vous et votre communauté, à vous connecter avec les autres."
    noDescription: "Pas de description"
    name: "Aide"
  staff:
    blacklist:
      list:
        description: "Afficher les entrées de la liste noire filtrées par utilisateurs ou serveurs"
    get:
      description: "Obtenir des informations sur les entités InterChat"
      server:
        description: "Obtenez des informations détaillées sur un serveur"
      hub:
        description: "Obtenez des informations détaillées sur un hub"
      user:
        description: "Obtenez des informations détaillées sur un utilisateur"
  profile:
    achievements:
      noneFound: "Non trouvée"
      noneFoundDescription: "Je n'ai trouvé aucune réussite pour cet utilisateur !"
    badges:
      noneFound: "Non trouvée"
      noneFoundDescription: "Je n'ai trouvé aucun badge pour cet utilisateur !"
  leaderboard:
    staffTag: "Personnel d'InterChat"
    userTag: "Utilisateur"
    messagesColumn: "Message"
    voteCountColumn: "Totale de vote"
  my:
    hubs:
      title: "Vos hubs"
      description: "Voici les hubs que vous possédez ou modérez :"
      position: "Positions:"
      owner: "Propriétaire"
  appeal:
    title: "Vos infractions susceptibles d'appel"
    description: "Sélectionnez une infraction ci-dessous pour soumettre un appel ou consultez l’état des appels existants."
    noInfractions:
      title: "Aucune infraction trouvée"
      description: "Vous n'avez pas d'infraction susceptible de faire l'objet d'un appel."
    notAppealable: "Cette infraction n’est plus susceptible d’appel."
  mod:
    panel:
      description: "Ouvrir le panneau de modération pour les utilisateurs, les messages ou les serveurs"
      contextMenu: "Panel de modération"
    ban:
      description: "Bannir un utilisateur ou un serveur du hub."
    mute:
      description: "Couper le son d'un utilisateur ou d'un serveur à partir d'un hub."
    warn:
      description: "Avenir un utilisateur ou un serveur du hub."
    unmute:
      description: "Révoquer les infractions de mise en sourdine actives pour un utilisateur ou un serveur dans un hub."
    unban:
      description: "Révoquer les infractions d'interdiction actives pour un utilisateur ou un serveur dans un hub."
    delete:
      description: "Supprimez un message InterChat à l'aide d'un lien de message Discord."
    delete_infraction:
      description: "Supprimer une infraction (Manager+ uniquement)."
  infractions:
    description: "Afficher les infractions dans un hub, filtrées par utilisateur ou serveur."
  rules:
    description: "Regarder les règles du hub"
    title: "Règles {hubName}"
    noRules:
      title: "Aucune règle établie"
      description: "Aucune règle n'a encore été définie pour le hub."
    footer: "{count} règles {plural} • Suivez ces directives pour maintenir une communauté positive"
    errors:
      noHub: "Cette commande doit être utilisée dans un canal serveur connecté à un hub, ou vous devez spécifier un nom de hub."
      notConnected: "Ce canal n'est connecté à aucun hub. Veuillez spécifier un nom de hub ou utiliser cette commande dans un canal connecté."
      hubNotFound: "Le hub \"{hubName}\" na pas été trouvée."
  connections:
    title: "Gestion des connexions"
    description: "Configurez et administrez toutes les connexions hub au sein de ce serveur."
    fields:
      lastActive: "Activité précédant"
    selected:
      description: "Vous pouvez maintenant modifier cette connexion. Utilisez les menus déroulants ci-dessous pour modifier le canal de diffusion ou les paramètres de connexion."
      fields:
        broadcastChannel: 'Diffusion à'
        connectionState: 'État de diffusion'
        lastActive: 'Activité précédant'
      state:
        enabled: 'Les diffusions sont désormais **actives**.'
        disabled: 'Les diffusions sont désormais **en pause**.'
    fix:
      title: 'Validation de la connexion'
      description: 'Toutes les connexions au sein de cette guilde ont été validées. Voir ci-dessous pour plus d''informations.'
      responses:
        success:
          fixed: 'Connexion fixée!'
          valid: 'Aucune issue a été trouvée.'
        errors:
          channelDeleted: 'La chaîne associée au hub a été supprimée. Connexion supprimée.'
          permissionsWebhook: 'Je ne parviens pas à utiliser les webhooks, veuillez vérifier mes autorisations et réessayer.'
          permissionsView: 'Je ne parviens pas à voir la chaîne, veuillez vérifier mes autorisations et réessayer.'
          permissionsSend: 'Je ne parviens pas à envoyer de messages, veuillez vérifier mes autorisations et réessayer.'

