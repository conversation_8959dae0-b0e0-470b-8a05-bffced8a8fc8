commands:
  about:
    title: "InterChat के बारे में"
    description_text: "InterChat Discord समुदायों को सक्रिय क्रॉस-सर्वर चर्चाओं के माध्यम से जोड़ता है। संदेश सर्वरों के बीच वास्तविक समय में स्वाभाविक रूप से प्रवाहित होते हैं, जिससे आपको सक्रिय और विषय-केंद्रित समुदाय बनाने में मदद मिलती है।"
    support_text: "मदद चाहिए? सहायता के लिए हमारे सपोर्ट सर्वर से जुड़ें!"
    features:
      title: "विशेषताएँ"
      list: |
        - सक्रिय क्रॉस-सर्वर चर्चाओं के लिए अन्य सर्वरों से जुड़ें
        - संदेश सर्वरों के बीच वास्तविक समय में स्वाभाविक रूप से प्रवाहित होते हैं
        - सक्रिय और विषय-केंद्रित समुदाय बनाएं
        - चर्चाओं को स्वस्थ बनाए रखने के लिए संयम उपकरण
        - अपने हब, सर्वर और सेटिंग्स को प्रबंधित करने के लिए दृश्य डैशबोर्ड
    buttons:
      vote: "top.gg पर वोट करें"
      invite: "InterChat को बुलाएँ"
      dashboard: "डैशबोर्ड खोलें"
      support: "सहायता सर्वर से जुड़ें"
      shardInfo: "Shard जानकारी"
  setup:
    welcome:
      title: "InterChat सेटअप में आपका स्वागत है"
      description: "आइए आपके सर्वर को कनेक्ट करें। यह विज़ार्ड आपको एक हब बनाने या उसमें शामिल होने में मदद करेगा।"
    options:
      create:
        label: "हब बनाएं"
        description: "अपना खुद का InterChat समुदाय शुरू करें"
      join:
        label: "हब से जुड़ें"
        description: "किसी मौजूदा समुदाय से जुड़ें"
    create:
      whatYoullCreate:
        title: "आप क्या बनाएँगे:"
        description: "{dot} आपके सर्वरों के लिए एक अनोखा सामुदायिक स्थान\n{dot} नियमों और संचालन पर पूरा नियंत्रण\n{dot} कस्टम सेटिंग्स और फीचर्स\n{dot} डिफ़ॉल्ट रूप से निजी - केवल आमंत्रण द्वारा"
      youllNeed:
        title: "आपको आवश्यकता होगी:"
        description: "{arrow_right} रचनात्मक हब नाम\n{arrow_right} संक्षिप्त विवरण\n{arrow_right} विस्तृत विवरण\n{arrow_right} लोगो इमेज URL"
    join:
      title: "हब से जुड़ें"
      description: "सार्वजनिक डायरेक्टरी का उपयोग करें या निमंत्रण कोड के साथ कनेक्ट करें।"
      publicHubs:
        title: "पब्लिक हब्स"
        description: "**ब्राउज़ करें** [interchat.tech/hubs](https://interchat.tech/hubs)\n**या नीचे दिए गए बटन पर क्लिक करें** डायरेक्टरी खोलने के लिए"
      privateHubs:
        title: "निजी या केवल आमंत्रण द्वारा"
        description: "**हब के मालिक या प्रबंधक से पूछें** आमंत्रण कोड के लिए  \n**अपने सर्वर में `/connect <निमंत्रण कोड>` चलाएँ** शामिल होने के लिए\n"
      footer: "आप कभी भी और हब में शामिल हो सकते हैं, और आप एक ही समय में विभिन्न चैनलों में कई हब से जुड़े हो सकते हैं।"
    nextSteps:
      created:
        title: "हब सफलतापूर्वक बनाया गया!"
        description: |
          आपका हब **{hubName}** तैयार है! अब आप ये कर सकते हैं:
          - अपना पहला सर्वर `/invite` का उपयोग करके आमंत्रित करें
          - `/rules set` के साथ नियम सेट करें
          - [डैशबोर्ड](https://interchat.tech/dashboard) में सेटिंग्स अनुकूलित करें
          - `/connect <hub-name>` के साथ चैनल कनेक्ट करें
        inviteLink:
          title: "अगला: आमंत्रण लिंक बनाएँ"
          description: "{hubInviteCommand} का उपयोग करके **{hubName}** के लिए आमंत्रण कोड जनरेट करें और उन्हें अन्य सर्वर मालिकों के साथ साझा करें।"
        shareHub:
          title: "अपने हब को साझा करें"
          description: "{dot} हमारे [सपोर्ट सर्वर]({supportInvite}) में अपने नए हब के बारे में पोस्ट करें\n{dot} दोस्तों और समुदायों के साथ साझा करें\n{dot} जानकारी फैलाने के लिए सोशल मीडिया का उपयोग करें"
        proTips:
          title: "{dot} प्रो टिप्स"
          description: "{dot} उन्नत सेटिंग्स के लिए [डैशबोर्ड]({website}) देखें\n{dot} अपने हब को सार्वजनिक करने के लिए {hubVisibilityCommand} का उपयोग करें\n{dot} मदद और अपडेट के लिए हमारे [सपोर्ट सर्वर]({supportInvite}) से जुड़ें"
        footer: "मदद चाहिए? हमारे सपोर्ट सर्वर से जुड़ें।"
  report:
    title: "संदेश रिपोर्ट करें"
    footer: "रिपोर्ट InterChat को सभी के लिए सुरक्षित बनाए रखने में मदद करती हैं"
    contextMenu: "संदेश रिपोर्ट करें"
    description: "{user} को **हब स्टाफ** को हब उल्लंघनों के लिए या **InterChat स्टाफ** को प्लेटफ़ॉर्म उल्लंघनों के लिए रिपोर्ट करें। उपयोगकर्ता को सूचित नहीं किया जाएगा, लेकिन मॉडरेटर देख सकते हैं कि रिपोर्ट किसने जमा की।"
    success:
      title: "रिपोर्ट जमा हो गई"
      toStaff: "{tick} आपकी रिपोर्ट InterChat स्टाफ को समीक्षा के लिए भेज दी गई है।"
      toHub: "{tick} आपकी रिपोर्ट हब मॉडरेटरों को समीक्षा के लिए भेज दी गई है।"
    errors:
      hubMessageOnly: "{x_icon} आप केवल InterChat हब्स के माध्यम से भेजे गए संदेशों की रिपोर्ट कर सकते हैं।"
      cannotReportSelf: "{x_icon} आप अपने खुद के संदेशों की रिपोर्ट नहीं कर सकते।"
  hubCreate:
    success:
      description: "आपका हब **{hubName}** तैयार है! अब आप ये कर सकते हैं:"
    errors:
      hubCreationFailed: "हब बनाते समय कुछ गलत हो गया। कृपया पुनः प्रयास करें। (PENDING REVIEW)."
      troubleshooting: "आप क्या कर सकते हैं:"
      troubleshootingSteps: "• जाँचें कि आपका लोगो URL मान्य है\n• कुछ देर बाद पुनः प्रयास करें\n• यदि समस्या बनी रहती है तो सपोर्ट से संपर्क करें"
  general:
    invite:
      title: "नमस्ते!"
      description: "InterChat चुनने के लिए धन्यवाद। मदद चाहिए? हमारे सपोर्ट सर्वर से जुड़ें। नीचे दिए गए बटनों का उपयोग करके हमारे बॉट को अपने सर्वर में आमंत्रित करें और सर्वरों को कनेक्ट करना शुरू करें।"
  stats:
    title: "InterChat आँकड़े"
    shard:
      title: "Shard जानकारी"
      statusReady: "तैयार"
      statusProvisioning: "प्रावधान हो रहा है..."
      current: "वर्तमान Shard: #{id}"
  help:
    title: "InterChat कमांड्स"
    description: "हमारे व्यापक कमांड्स की सूची देखें जो आपको और आपके समुदाय को दूसरों से जुड़ने में मदद करती है।"
    noDescription: "कोई विवरण नहीं"
    name: "मदद"
  staff:
    blacklist:
      list:
        description: "उपयोगकर्ताओं या सर्वरों के आधार पर ब्लैकलिस्ट प्रविष्टियाँ देखें"
    get:
      description: "InterChat संस्थाओं के बारे में जानकारी प्राप्त करें"
      server:
        description: "किसी सर्वर के बारे में विस्तृत जानकारी प्राप्त करें"
      hub:
        description: "किसी हब के बारे में विस्तृत जानकारी प्राप्त करें"
      user:
        description: "किसी उपयोगकर्ता के बारे में विस्तृत जानकारी प्राप्त करें"
  profile:
    achievements:
      noneFound: "कुछ नहीं मिला"
      noneFoundDescription: "मुझे इस उपयोगकर्ता के लिए कोई उपलब्धि नहीं मिली!"
    badges:
      noneFound: "कुछ नहीं मिला"
      noneFoundDescription: "मुझे इस उपयोगकर्ता के लिए कोई बैज नहीं मिला!"
  leaderboard:
    staffTag: "InterChat स्टाफ"
    userTag: "उपयोगकर्ता"
    messagesColumn: "संदेश"
    voteCountColumn: "वोट संख्या"
  my:
    hubs:
      title: "आपके हब्स"
      description: "यहाँ वे हब्स हैं जिन्हें आप स्वामित्व या मॉडरेट करते हैं:"
      position: "स्थिति:"
      owner: "मालिक"
  appeal:
    title: "आपकी अपील योग्य उल्लंघनाएँ"
    description: "नीचे एक उल्लंघन चुनें अपील जमा करने के लिए, या मौजूदा अपीलों की स्थिति देखें।"
    noInfractions:
      title: "कोई सक्रिय उल्लंघन नहीं"
      description: "आपके पास कोई सक्रिय उल्लंघन नहीं है जो अपील योग्य हो।"
    notAppealable: "यह उल्लंघन अब अपील योग्य नहीं है।"
  mod:
    panel:
      description: "उपयोगकर्ताओं, संदेशों, या सर्वरों के लिए मॉडरेशन पैनल खोलें"
      contextMenu: "मॉड पैनल"
    ban:
      description: "किसी उपयोगकर्ता या सर्वर को हब से प्रतिबंधित करें।"
    mute:
      description: "किसी उपयोगकर्ता या सर्वर को हब से म्यूट करें।"
    warn:
      description: "किसी उपयोगकर्ता या सर्वर को हब से चेतावनी दें।"
    unmute:
      description: "किसी उपयोगकर्ता या सर्वर के सक्रिय म्यूट को हब में रद्द करें।"
    unban:
      description: "किसी उपयोगकर्ता या सर्वर के सक्रिय प्रतिबंध को हब में रद्द करें।"
    delete:
      description: "Discord संदेश लिंक का उपयोग करके एक InterChat संदेश हटाएँ।"
    delete_infraction:
      description: "एक उल्लंघन हटाएँ (केवल प्रबंधक+ के लिए)।"
  infractions:
    description: "हब में उल्लंघन देखें, उपयोगकर्ता या सर्वर द्वारा फ़िल्टर किए गए।"
  rules:
    description: "किसी हब के नियम देखें"
    title: "{hubName} नियम"
    noRules:
      title: "कोई नियम सेट नहीं किए गए"
      description: "इस हब के लिए अभी तक कोई नियम सेट नहीं किए गए हैं।"
    footer: "{count} नियम{plural} • सकारात्मक समुदाय बनाए रखने के लिए इन दिशानिर्देशों का पालन करें"
    errors:
      noHub: "यह कमांड किसी ऐसे सर्वर चैनल में उपयोग की जानी चाहिए जो किसी हब से जुड़ा हो, या आपको एक हब नाम निर्दिष्ट करना होगा।"
      notConnected: "यह चैनल किसी भी हब से जुड़ा नहीं है। कृपया हब नाम निर्दिष्ट करें या इस कमांड का उपयोग किसी जुड़े हुए चैनल में करें।"
      hubNotFound: "हब \"{hubName}\" नहीं मिला।"
  connections:
    title: "कनेक्शन प्रबंधन"
    description: "इस सर्वर के भीतर सभी हब कनेक्शनों को कॉन्फ़िगर और प्रबंधित करें।"
    fields:
      lastActive: "अंतिम सक्रिय"
    selected:
      description: "अब आप इस कनेक्शन को संपादित कर सकते हैं। नीचे दिए गए विकल्पों का उपयोग करके प्रसारण चैनल बदलें या कनेक्शन से संबंधित सेटिंग्स संपादित करें।"
      fields:
        broadcastChannel: 'प्रसारण चैनल (PENDING REVIEW)'
        connectionState: 'प्रसारण स्थिति'
        lastActive: 'अंतिम सक्रिय'
      state:
        enabled: 'प्रसारण अब **सक्रिय** हैं।'
        disabled: 'प्रसारण अब **रुके हुए** हैं।'
    fix:
      title: 'कनेक्शन मान्यता'
      description: 'इस गिल्ड के भीतर सभी कनेक्शनों को मान्य किया गया है। अधिक जानकारी के लिए नीचे देखें।'
      responses:
        success:
          fixed: 'कनेक्शन ठीक कर दिया गया!'
          valid: 'कोई समस्या नहीं मिली।'
        errors:
          channelDeleted: 'इस हब से जुड़ा चैनल हटा दिया गया है। कनेक्शन हटा दिया गया।'
          permissionsWebhook: 'मैं वेबहुक्स उपयोग नहीं कर पा रहा हूँ, कृपया मेरी अनुमतियाँ जाँचें और पुनः प्रयास करें।'
          permissionsView: 'मैं चैनल देख नहीं पा रहा हूँ, कृपया मेरी अनुमतियाँ जाँचें और पुनः प्रयास करें।'
          permissionsSend: 'मैं संदेश भेज नहीं पा रहा हूँ, कृपया मेरी अनुमतियाँ जाँचें और पुनः प्रयास करें।'

