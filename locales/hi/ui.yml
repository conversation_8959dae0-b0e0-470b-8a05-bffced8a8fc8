ui:
  setup:
    embed:
      description: "अपने हब का विवरण दर्ज करने के लिए मोडल का उपयोग करें।"
    select:
      placeholder: "भाषा चुनें"
      loadingLabel: "लोड हो रहा है..."
      loadingDescription: "कृपया प्रतीक्षा करें..."
      chooseOption: "शुरू करने के लिए एक विकल्प चुनें..."
    buttons:
      back: "वापस"
      cancel: "रद्द करें"
      refresh: "रीफ्रेश"
      enterHubInfo: "हब जानकारी दर्ज करें"
      hubInfoComplete: "हब जानकारी पूरी हुई"
      createHub: "हब बनाएँ"
      completeInfoFirst: "पहले हब जानकारी पूरी करें"
      discoverHubs: "सार्वजनिक हब खोजें"
      supportServer: "सपोर्ट सर्वर"
  common:
    titles:
      error: "त्रुटि!"
      success: "सफलता!"
      cancelled: 'रद्द किया गया!'
    noDescription: "कोई विवरण नहीं"
    labels:
      date: "तारीख"
      create: 'बनाएँ'
      cancel: 'रद्द करें'
    messages:
      loading: "लोड हो रहा है..."
      pleaseWait: "कृपया प्रतीक्षा करें..."
      notImplemented: "अभी लागू नहीं हुआ।"
      hubNotFound: "हब नहीं मिला। कृपया बाद में पुनः प्रयास करें।"
      serverNotFound: "सर्वर नहीं मिला।"
      userNotFound: "उपयोगकर्ता नहीं मिला।"
      notSpecified: "निर्दिष्ट नहीं"
      interfacePermission: "आप इस इंटरफ़ेस का उपयोग नहीं कर सकते।"
      hubUpdateFailed: "हब सेटिंग्स अपडेट करने में विफल।"
      hubConfigDescription: "अपने समुदाय के लिए InterChat हब को अनुकूलित करें। नीचे चुनें कि आप क्या कॉन्फ़िगर करना चाहते हैं।"
    pagination:
      previous: "पिछला"
      next: "अगला"
      page: "पेज {current}/{total}"
    errors:
      notYourMenu: "यह आपका मेनू नहीं है।"
      cannotControlPagination: "आप इस पृष्ठांकन को नियंत्रित नहीं कर सकते।"
      notFound: "नहीं मिला"
      invalidFilter: "अमान्य फ़िल्टर"
    modal:
      hubCreation:
        title: "अपना हब बनाएं"
        name:
          label: "हब का नाम"
          placeholder: "अपने हब के लिए एक अद्वितीय नाम दर्ज करें"
        brief:
          label: "संक्षिप्त विवरण"
          placeholder: "अपने हब के उद्देश्य का एक त्वरित सारांश दें"
        description:
          label: "विस्तृत विवरण"
          placeholder: "लोगों को बताएं कि आपका हब किस बारे में है और वे क्या अपेक्षा कर सकते हैं"
        logoUrl:
          label: "हब लोगो URL"
          placeholder: "https://example.com/your-logo.png"
    create:
      footer:
        getStarted: "शुरू करने के लिए नीचे 'हब जानकारी दर्ज करें' पर क्लिक करें!"
  help:
    select:
      placeholder: "श्रेणी चुनें"
  preferences:
    title: "उपयोगकर्ता प्राथमिकताएँ"
    description: "अपने व्यक्तिगत सेटिंग्स को संपादित करें ताकि InterChat आपके लिए सही ढंग से काम करे। ये GLOBAL हैं और सभी सर्वरों में लागू होंगे जहाँ आप InterChat का उपयोग करते हैं।"
    buttons:
      return: "वापस"
    replyMentions:
      title: "प्राथमिकताएँ: उत्तर में उल्लेख"
      description: "अब आप **{status}** होंगे जब कोई आपके संदेशों का उत्तर देगा। आप इसे नीचे बटन से टॉगल कर सकते हैं, या मेनू में वापस लौट सकते हैं।"
      currentDescription: "वर्तमान में आप **{status}** हैं जब आपके संदेशों में उत्तर दिया जाता है। आप इसे नीचे बटन से टॉगल कर सकते हैं, या मेनू में वापस लौट सकते हैं।"
      mentioned: "उल्लेखित"
      notMentioned: "अउल्लेखित"
    errors:
      noPreferences: "आपने अभी तक प्राथमिकताएँ सेट नहीं की हैं।"
      hide: "छुपाएँ"
    general:
      title: "सामान्य प्राथमिकताएँ"
      description: "InterChat के भीतर सामान्य प्राथमिकताओं को कॉन्फ़िगर करें। यह सुनिश्चित करता है कि आपका अनुभव यथासंभव बेहतर हो।"
    locale:
      title: "भाषा चयन"
      description: "InterChat के लिए अपनी पसंदीदा भाषा चुनें।"
    select:
      placeholder: "एक विकल्प चुनें"
      category: "एक श्रेणी चुनें"
    badges:
      title: "प्राथमिकताएँ: बैज"
      description: "आपके बैज अब आपके संदेशों में **{status}** हैं। आप इसे नीचे बटन से टॉगल कर सकते हैं, या मेनू में वापस लौट सकते हैं।"
      currentDescription: "वर्तमान में आपके बैज आपके संदेशों में **{status}** हैं। आप इसे नीचे बटन से टॉगल कर सकते हैं, या मेनू में वापस लौट सकते हैं।"
      visible: "दिखाई दे रहे हैं"
      hidden: "छुपाए गए"
      buttons:
        show: "दिखाएँ"
        hide: "छुपाएँ"
  appeal:
    buttons:
      previous: "पिछला"
      next: "अगला"
    select:
      placeholder: "अपील करने के लिए एक उल्लंघन चुनें"
    errors:
      cannotControl: "आप इस पृष्ठांकन मेनू को नियंत्रित नहीं कर सकते।"
      notYourMenu: "यह आपका अपील मेनू नहीं है।"
      nothingSelected: "कुछ भी नहीं चुना गया था।"
      invalidSelection: "अमान्य चयन।"
      modalError: "अपील मोडल खोलने में विफल।"
    modal:
      title: "अपील सबमिट करें"
      q1: "आपको क्यों लगता है कि यह कार्रवाई गलत थी?"
      q2: "हमें स्थिति के बारे में बताएं"
      q3: "अतिरिक्त जानकारी"
    success:
      submitted: "{tick} आपकी अपील समीक्षा के लिए सबमिट कर दी गई है।"
    viewInfractions:
      title: "हाल के उपयोगकर्ता उल्लंघन"
      empty: "इस उपयोगकर्ता के लिए कोई उल्लंघन नहीं मिला।"
    status:
      acceptedBy: "@{name} द्वारा स्वीकृत"
      rejectedBy: "@{name} द्वारा अस्वीकृत"
    actions:
      decisionTitle: "अपील का निर्णय"
      reasonOptional:
        label: "कारण (वैकल्पिक)"
        placeholder: "अपने निर्णय की व्याख्या करें..."
  report:
    buttons:
      toStaff: "स्टाफ को रिपोर्ट करें"
      toHub: "हब को रिपोर्ट करें"
    modal:
      toStaff:
        title: "InterChat स्टाफ को रिपोर्ट करें"
      toHub:
        title: "हब मॉडरेटर को रिपोर्ट करें"
      reason:
        label: "रिपोर्ट का कारण"
        placeholder: "कृपया बताएं कि आप इस संदेश की रिपोर्ट क्यों कर रहे हैं..."
    status:
      resolvedBy: "{name} द्वारा हल किया गया"
      ignoredBy: "{name} द्वारा अनदेखा किया गया"
  hubConfig:
    main:
      placeholder: "एक विकल्प चुनें"
      loadingLabel: "लोड हो रहा है..."
      loadingDescription: "कृपया प्रतीक्षा करें।"
    general:
      placeholder: "कॉन्फ़िगर करने के लिए एक सेटिंग चुनें..."
      editDescription:
        label: "विवरण संपादित करें"
        description: "अपने हब का विवरण टेक्स्ट अपडेट करें"
      editName:
        label: "हब का नाम संपादित करें"
        description: "अपने हब का नाम बदलें (10 दिन का कूलडाउन)"
      welcomeMessage:
        label: "स्वागत संदेश"
        description: "नए उपयोगकर्ताओं को दिखाया जाने वाला संदेश सेट करें"
      toggleNsfw:
        label: "NSFW टॉगल करें"
        description: "अपने हब को NSFW के रूप में चिह्नित करें या नहीं"
      togglePrivate:
        label: "निजी टॉगल करें"
        description: "अपने हब को निजी के रूप में चिह्नित करें या नहीं"
    permissions:
      remove:
        label: "हटाएँ"
      moderator:
        label: "मॉडरेटर"
        description: "संदेश प्रबंधित करें, उपयोगकर्ताओं को मॉडरेट करें, रिपोर्ट और अपील संभालें"
      manager:
        label: "मैनेजर"
        description: "हब सेटिंग्स प्रबंधित करें और वह सब कुछ जो मॉडरेटर कर सकते हैं"
      managerOnly:
        description: "हब सेटिंग्स प्रबंधित करें"
    modules:
      reactions:
        description: "संदेशों पर प्रतिक्रिया-आधारित इंटरैक्शन की अनुमति दें"
      hideLinks:
        description: "हब संदेशों में लिंक पूर्वावलोकन छिपाएँ"
      spamFilter:
        description: "स्पैम और अवांछित सामग्री को स्वचालित रूप से फ़िल्टर करें"
      blockInvites:
        description: "संदेशों में Discord सर्वर आमंत्रणों को ब्लॉक करें"
      useNicknames:
        description: "उपयोगकर्ता नामों के बजाय उपयोगकर्ता उपनाम प्रदर्शित करें"
    logging:
      moderationLogs: "मॉडरेशन लॉग"
      joinLeaveLogs: "शामिल होने/छोड़ने के लॉग"
      appealsChannel: "अपील चैनल"
      reportsChannel: "रिपोर्ट चैनल"
      networkAlerts: "नेटवर्क अलर्ट"
      messageModeration: "संदेश मॉडरेशन"
    invites:
      create:
        title: "इनपुट आवश्यक है"
        customCode:
          label: "कस्टम कोड"
          placeholder: "abc123"
        uses:
          label: "उपयोग"
          placeholder: "अनंत के लिए खाली छोड़ दें"
        expiry:
          label: "समाप्ति"
          placeholder: "1 सप्ताह"
      buttons:
        create: "बनाएँ"
  moderation:
    actionNames:
      warned: "चेतावनी दी गई"
      muted: "म्यूट किया गया"
      banned: "प्रतिबंधित किया गया"
    actionNouns:
      mute: "म्यूट"
      ban: "प्रतिबंध"
    prep:
      in: "में"
      from: "से"
    actions:
      warn:
        label: "चेतावनी दें"
        description: "चयनित लक्ष्य को चेतावनी जारी करें"
      mute:
        label: "म्यूट करें"
        description: "निर्दिष्ट हब से उपयोगकर्ता/सर्वर को म्यूट करें"
      ban:
        label: "प्रतिबंधित करें"
        description: "निर्दिष्ट हब से उपयोगकर्ता/सर्वर को प्रतिबंधित करें"
      unmute:
        label: "अनम्यूट करें"
        description: "इस हब में एक सक्रिय म्यूट रद्द करें"
      unban:
        label: "अनबैन करें"
        description: "इस हब में एक सक्रिय प्रतिबंध रद्द करें"
      blacklist:
        label: "ब्लैकलिस्ट"
        description: "एक इंटरचैट व्यापी ब्लैकलिस्ट जारी करें (उपयोगकर्ता/सर्वर)"
      delete:
        label: "हटाएं"
        description: "सभी जुड़े हुए हब में चयनित संदेश को हटाएं"
    modal:
      title: "मॉडरेशन कार्रवाई"
      reason:
        label: "कारण"
        placeholder: "एक संक्षिप्त औचित्य दर्ज करें{optional}"
      duration:
        label: "अवधि"
        placeholder: "उदा. 30m, 2h, 1d"
    hubSelect:
      placeholder: "मॉडरेट करने के लिए एक हब चुनें..."
    hubSelection:
      title: "हब चयन"
      description: "वह हब चुनें जिसे आप मॉडरेट करना चाहते हैं।"
      fieldHubLabel: "हब"
      fieldHubPrompt: "चयनित हब"
      prompt: "मॉडरेट करने के लिए एक हब चुनें"
    targetSelection:
      title: "लक्ष्य चयन"
      description: "आप किसे {action} करना चाहते हैं?"
      userField: "उपयोगकर्ता"
      serverField: "सर्वर"
    actionSelect:
      placeholder: "एक मॉडरेशन कार्रवाई चुनें..."
    fields:
      reason: "कारण"
  infractions:
    buttons:
      userInfractions: "उपयोगकर्ता उल्लंघन"
      serverInfractions: "सर्वर उल्लंघन"
    titles:
      base: "उल्लंघन · {hubName}"
      userList: "उपयोगकर्ता उल्लंघन · {hubName}"
      serverList: "सर्वर उल्लंघन · {hubName}"
      userSpecific: "{user} के लिए उल्लंघन · {hubName}"
      serverSpecific: "सर्वर {serverId} के लिए उल्लंघन · {hubName}"
    descriptions:
      base: "इस हब के लिए देखने हेतु उल्लंघन चुनें।"
      userListEmpty: "इस हब के लिए कोई उपयोगकर्ता उल्लंघन नहीं मिला।"
      serverListEmpty: "इस हब के लिए कोई सर्वर उल्लंघन नहीं मिला।"
      userSpecificEmpty: "इस हब में इस उपयोगकर्ता के लिए कोई उल्लंघन नहीं मिला।"
      serverSpecificEmpty: "इस हब में इस सर्वर के लिए कोई उल्लंघन नहीं मिला।"
    labels:
      infraction: "उल्लंघन"
    fields:
      userName: "उपयोगकर्ता का नाम"
      userId: "उपयोगकर्ता आईडी"
      serverName: "सर्वर का नाम"
      serverId: "सर्वर ID"
      reason: "कारण"
      moderator: "मॉडरेटर"
      issued: "जारी किया गया"
      status: "स्थिति"
  staff:
    hub:
      section:
        status: "स्थिति"
        moderation: "मॉडरेशन"
      fields:
        name: "नाम"
        messages: "संदेश"
        connections: "कनेक्शन"
        created: "बनाया गया"
        lastActive: "अंतिम सक्रिय"
        upvotes: "अपवोट"
        owner: "मालिक"
        location: "स्थान"
        activity: "गतिविधि"
        appealCooldown: "अपील कूलडाउन"
        welcomeMessage: "स्वागत संदेश"
        description: "विवरण"
      values:
        messagesThisWeek: "इस हफ़्ता **{count}**"
        connectionsActive: "**{count}** सक्रिय"
        upvotesTotal: "कुल **{count}**"
      badges:
        verified: "सत्यापित"
        partnered: "साझेदार"
        featured: "विशेष रुप से प्रदर्शित"
        private: "निजी"
        locked: "लॉक किया हुआ"
        nsfw: "NSFW"
    server:
      fields:
        name: "नाम"
        serverId: "सर्वर ID"
        inviteCode: "आमंत्रण कोड"
        messages: "संदेश"
        connections: "कनेक्शन"
        status: "स्थिति"
        created: "बनाया गया"
        lastMessage: "अंतिम संदेश"
        updated: "अद्यतन किया गया"
      values:
        messagesTotal: "कुल **{count}**"
        connectionsActive: "**{count}** सक्रिय"
        premium: "प्रीमियम"
        standard: "मानक"
    user:
      fields:
        name: "नाम"
        userId: "उपयोगकर्ता ID"
        status: "स्थिति"
        messages: "संदेश"
        reputation: "प्रतिष्ठा"
        votes: "वोट"
        hubJoins: "हब जॉइन"
        engagement: "जुड़ाव"
        locale: "भाषा"
        created: "बनाया गया"
        lastMessage: "अंतिम संदेश"
        lastVote: "अंतिम वोट"
      values:
        staff: "स्टाफ"
        member: "सदस्य"
        messagesSent: "**{count}** भेजे गए"
        reputationPoints: "**{count}** अंक"
        votesCast: "**{count}** डाले गए"
        hubJoinsTotal: "कुल **{count}**"
        notSet: "सेट नहीं है"
        never: "कभी नहीं"
      sections:
        preferences: "प्राथमिकताएँ"
        createdContent: "निर्मित सामग्री"
        activity: "गतिविधि"
        donation: "डोनेशन"
        badges: "बैज"
      preferences:
        showBadges: "बैज दिखाएँ"
        mentionOnReply: "उत्तर पर उल्लेख करें"
        showNsfwHubs: "NSFW हब दिखाएँ"
      content:
        hubs: "{count} हब"
        modPositions: "{count} मॉड पद"
        blockedWords: "{count} अवरुद्ध शब्द"
        antiSwearRules: "{count} अपशब्द-विरोधी नियम"
      moderation:
        infractionsReceived: "{count} उल्लंघन प्राप्त हुए"
        infractionsIssued: "{count} उल्लंघन जारी किए गए"
        blacklistsReceived: "{count} ब्लैकलिस्ट प्राप्त हुए"
        blacklistsIssued: "{count} ब्लैकलिस्ट जारी किए गए"
      activity:
        appeals: "{count} अपीलें"
        reviews: "{count} समीक्षाएं"
        reportsMade: "{count} रिपोर्ट की गईं"
        reportsReceived: "{count} रिपोर्ट प्राप्त हुईं"
        achievements: "{count} उपलब्धियां"
      donation:
        tier: "टीयर: {tier}"
        expires: "समाप्त होता है"
    blacklist:
      titles:
        list: "ब्लैकलिस्ट प्रविष्टियाँ"
        searchResults: "ब्लैकलिस्ट खोज परिणाम"
        userList: "उपयोगकर्ता ब्लैकलिस्ट"
        serverList: "सर्वर ब्लैकलिस्ट"
      descriptions:
        list: "कुल ब्लैकलिस्ट प्रविष्टियाँ"
        noEntriesPage: "इस पेज पर कोई प्रविष्टि नहीं है।"
        userListEmpty: "कोई ब्लैकलिस्टेड उपयोगकर्ता नहीं मिला।"
        serverListEmpty: "कोई ब्लैकलिस्टेड सर्वर नहीं मिला।"
      fields:
        user: "उपयोगकर्ता"
        server: "सर्वर"
        expires: "समाप्त होता है"
        staff: "स्टाफ"
        added: "जोड़ा गया"
      labels:
        user: "उपयोगकर्ता"
        server: "सर्वर"
  hub:
    title: 'हब निर्माण'
    description: 'एक समुदाय, अपने दोस्तों... या किसी के लिए भी अपना खुद का इंटरचैट हब बनाएं - सूची युगों तक चल सकती है। आगे बढ़ो, देखो यह तुम्हें कहाँ ले जाता है।'
    delete:
      description: 'आपका हब हटा दिया गया है।'
    connect:
      success:
        title: 'जुड़ गया!'
        description: '**{hubName}** से जुड़ गए - बातें शुरू करें!'
        fieldValue: 'जुड़ा हुआ चैनल: {channel}'
      errors:
        alreadyConnected: 'अरे नहीं! ऐसा लगता है कि इस सर्वर का इस हब से पहले से ही एक कनेक्शन है।'
    disconnect:
      title: 'डिस्कनेक्ट हो गया!'
      description: 'हब से डिस्कनेक्ट हो गया।'
      fieldValue: 'संबंधित चैनल: {channel}'
    invites:
      title: 'सक्रिय आमंत्रण'
      inviteUses: '**उपयोग:**'
      inviteExpire: '**समाप्त होता है:**'
      noneFound: 'कोई नहीं मिला!'
    announcements:
      title: 'हब घोषणाएँ'
      description: "एक **एक बार** की घोषणा बनाएं जो सभी जुड़े चैनलों में भेजी जाएगी। \n\n-# **आवर्ती** घोषणाएं बनाने के लिए कृपया हब कॉन्फ़िगरेशन मेनू पर जाएं।"
    creation:
      title: "आइए आपका हब सेट करें!"
      description: "हब वेबहुक का उपयोग करके कई सर्वरों को एक साथ जोड़ते हैं। वे क्रॉस-सर्वर समुदायों के लिए बहुत अच्छे हैं।"
      clickToBegin: "शुरू करने के लिए {emoji} बनाएं पर क्लिक करें।"
      modal:
        title: 'हब जानकारी'
        hubName: "हब का नाम"
        shortLabel: "संक्षिप्त विवरण"
      errors:
        invalidInput: 'अमान्य इनपुट'
        name: 'अरे नहीं! आपके हब का नाम अक्षरों, संख्याओं, रिक्त स्थान और अंडरस्कोर से बना होना चाहिए। हम किसी अन्य वर्ण की अनुमति नहीं देते हैं।'
        shortDescription: "हब का संक्षिप्त विवरण 10 और 100 वर्णों के बीच होना चाहिए।"
        unhandled: 'उफ़! आपके हब को बनाने का प्रयास करते समय कुछ गलत हो गया। कृपया पुनः प्रयास करें, और यदि समस्या बनी रहती है तो हमारी (समर्थन टीम)[supportServer] से संपर्क करें।'
        unique: 'अरे नहीं! वह हब नाम पहले ही लिया जा चुका है। आपको दूसरा चुनना होगा।'
      visibility:
        title: 'हब दृश्यता'
        description: 'क्या आपका हब **सार्वजनिक** होना चाहिए? इसका मतलब है कि यह **सभी** के लिए सुलभ होगा और [डिस्कवरी पेज]({discoverHubs}) पर दिखाई देगा।'
        buttons:
          public: 'सार्वजनिक'
          private: 'निजी'
      preview:
        title: 'पूर्ण!'
        description: 'आपने हब सेटअप पूरा कर लिया है! जारी रखने के लिए नीचे अपने विकल्पों की समीक्षा करें।'
        footer: 'आप निर्माण के बाद, बाद में और फ़ील्ड संपादित कर सकते हैं।'
        fields:
          title: 'जानकारी'
          value: |
            > {nemoji} **नाम:** {hubName}
            > {sdemoji} **संक्षिप्त विवरण:** {shortDescription}
            > {vemoji} **दृश्यता:** {visibility}
            > {ldemoji} **लंबा विवरण:** कोई नहीं - इसे बाद में [डैशबोर्ड]({dashboard}) में सेट करें!
      complete:
        title: 'बनाया गया!'
        description: "आपका हब अब तैयार है - बातें शुरू करें! और अनुकूलन की तलाश है? [डैशबोर्ड]({dashboard}) पर जाएं और अपने हब की सेटिंग्स कॉन्फ़िगर करें।"
        private: '**उपयोगकर्ताओं को आमंत्रित करें!** चूंकि आपका हब निजी है, आपको उपयोगकर्ता आमंत्रण बनाने होंगे। `/hub invites` के साथ एक बनाएं!'
        public: '**आपकी रचना!** चूंकि आपका हब सार्वजनिक है, आप इसे एक्सप्लोरर पर देख सकते हैं - [इसे देखें]({public})!'
      other:
        cancelled: 'सेटअप रद्द कर दिया गया। हम आपको जल्द ही यहां वापस देखने की उम्मीद करते हैं।'
    config:
      title: "हब कॉन्फ़िगरेशन"
      description: "अपने समुदाय के लिए InterChat हब को अनुकूलित करें। नीचे चुनें कि आप क्या कॉन्फ़िगर करना चाहते हैं।"
      options:
        general:
          label: "सामान्य सेटिंग्स"
          description: "अपने हब की उपस्थिति, नियम, और बहुत कुछ अनुकूलित करें"
        rules:
          label: "हब के नियम"
          description: "अपने हब के सामुदायिक नियमों और दिशानिर्देशों का प्रबंधन करें"
        team:
          label: "टीम प्रबंधन"
          description: "अपने हब की टीम और स्टाफ अनुमतियों का प्रबंधन करें"
        modules:
          label: "मॉड्यूल"
          description: "अपने हब के भीतर अतिरिक्त मॉड्यूल सक्षम या अक्षम करें"
        logging:
          label: "लॉगिंग कॉन्फ़िगरेशन"
          description: "अपने हब के लिए लॉगिंग चैनल और अधिसूचना भूमिकाएं कॉन्फ़िगर करें"
        transfer:
          label: "स्वामित्व हस्तांतरण"
          description: "अपने हब का स्वामित्व किसी अन्य व्यक्ति को हस्तांतरित करें"
        announcements:
          label: "अनुसूचित घोषणाएँ"
          description: "अपने हब के लिए स्वचालित अनुसूचित घोषणाएं कॉन्फ़िगर करें"
    all:
      title: "इंटरचैट हब"
      description: "नीचे आप हमारे सभी हब की सूची में खोज कर सकेंगे! उन्हें देखें, देखें कि आपको कौन मिलता है।"
      filteredBy: 'परिणाम फ़िल्टर किए गए: {filter}'
      errors:
        notFound: "अरे नहीं! मुझे कोई हब नहीं मिला। शायद ऐसा नहीं होना चाहिए था। कृपया हमारी [समर्थन टीम]({supportServer}) से संपर्क करें।"
        invalidFilter: "कुछ नहीं... कोई हब आपके फ़िल्टर से मेल नहीं खाता।"
      modal:
        title: 'खोज फ़िल्टर'
        searchFilter:
          label: 'फ़िल्टर'
          placeholder: 'इंटरचैट सेंट्रल'
    rules:
      title: "हब नियम प्रबंधन"
      description: "नीचे अपने हब के सामुदायिक नियमों और दिशानिर्देशों का प्रबंधन करें।"
      noRules: "इस हब के लिए अभी तक कोई नियम सेट नहीं किए गए हैं।"
      selectRule:
        placeholder: "संपादित करने या हटाने के लिए एक नियम चुनें..."
      addRule:
        label: "नियम जोड़ें"
        modal:
          title: "नया नियम जोड़ें"
          placeholder: "अपना नया नियम यहां दर्ज करें..."
      editRule:
        label: "नियम संपादित करें"
        modal:
          title: "नियम संपादित करें"
          placeholder: "अपना अद्यतन नियम यहां दर्ज करें..."
      deleteRule:
        label: "नियम हटाएं"
        confirmation: "क्या आप वाकई इस नियम को हटाना चाहते हैं?"
      moveUp:
        label: "ऊपर ले जाएँ"
      moveDown:
        label: "नीचे ले जाएँ"
      viewAll:
        label: "सभी नियम देखें"
      validation:
        empty: "नियम खाली नहीं हो सकता।"
        tooLong: "नियम {maxLength} वर्ण या उससे कम का होना चाहिए।"
        maxRules: "अधिकतम {maxRules} नियमों की अनुमति है।"
      success:
        added: "नियम सफलतापूर्वक जोड़ा गया!"
        updated: "नियम सफलतापूर्वक अपडेट किया गया!"
        deleted: "नियम सफलतापूर्वक हटा दिया गया!"
        moved: "नियम सफलतापूर्वक स्थानांतरित किया गया!"
      errors:
        maxRulesReached: "अधिकतम नियमों तक पहुंच गया ({maxRules})"
        noRulesAvailable: "चुनने के लिए कोई नियम उपलब्ध नहीं है"
        sessionExpired: "सत्र समाप्त हो गया"
        sessionExpiredDescription: "यह कॉन्फ़िगरेशन सत्र समाप्त हो गया है। नियमों का प्रबंधन जारी रखने के लिए कृपया कमांड फिर से चलाएं।"
        actionCancelled: "कार्रवाई रद्द"
        actionCancelledDescription: "नियम कार्रवाई रद्द कर दी गई है। कोई बदलाव नहीं किया गया।"
        ruleNotFound: "अमान्य नियम चयन। नियम किसी अन्य उपयोगकर्ता द्वारा हटाया जा सकता है।"
        invalidRuleSelection: "अमान्य नियम चयन। कृपया पुनः प्रयास करें।"
        processingError: "आपके अनुरोध को संसाधित करते समय एक त्रुटि हुई। कृपया पुनः प्रयास करें।"
        refreshError: "ताज़ा करते समय एक त्रुटि हुई। कृपया पुनः प्रयास करें।"
        navigationError: "नेविगेट करते समय एक त्रुटि हुई। कृपया पुनः प्रयास करें।"
        selectionError: "नियम का चयन करते समय एक त्रुटि हुई। कृपया पुनः प्रयास करें।"
        deletionCancelled: "रद्द किया गया"
        deletionCancelledDescription: "नियम हटाना रद्द कर दिया गया है। कोई बदलाव नहीं किया गया।"
      warnings:
        deleteWarning: "चेतावनी"
        deleteWarningDescription: "यह क्रिया पूर्ववत नहीं की जा सकती। नियम आपके हब से स्थायी रूप से हटा दिया जाएगा।"
      display:
        noRulesTitle: "कोई नियम सेट नहीं किए गए"
        noRulesDescription: "इस हब के लिए अभी तक कोई नियम निर्धारित नहीं किया गया है। अपना पहला नियम बनाने के लिए नीचे \"नियम जोड़ें\" बटन का उपयोग करें।"
        currentRulesTitle: "वर्तमान नियम ({current}/{max})"
        ruleSelected: "नियम {number} चयनित"
        currentRule: "वर्तमान नियम:"
  connection:
    fixAll:
      label: "सभी कनेक्शन ठीक करें"
    manage:
      placeholder: "प्रबंधित करने के लिए एक कनेक्शन चुनें"
