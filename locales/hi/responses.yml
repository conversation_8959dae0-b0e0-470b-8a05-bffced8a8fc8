responses:
  common:
    unknown: "अज्ञात"
    hub: "हब"
  setup:
    setupComplete: "तैयार हब बनाने के लिए? Create Hub बटन पर क्लिक करें!"
    editMessagePrompt: "{emoji} कृपया अपना संदेश संपादित करने के लिए मोडल का उपयोग करें।"
    preview:
      titleSaved: "{emoji} हब की जानकारी सहेज ली गई!"
      previewTitle: "यहाँ आपका हब प्रीव्यू है:"
      name: "{emoji} नाम"
      short: "{emoji} छोटा नाम"
      description: "{emoji} विवरण"
    locale:
      successTitle: "सफलता!"
      successDescription: "{tick} आपकी भाषा **{locale_name}** में सेट कर दी गई है"
    loading:
      creatingHub: "{loading} आपका हब बनाया जा रहा है..."
      pleaseWait: "कृपया प्रतीक्षा करें, हम आपका समुदाय स्पेस सेट कर रहे हैं।"
    errors:
      hubCreationFailed: "{no} हब बनाने में असफल"
  appeal:
    constants:
      unknownHub: "अज्ञात हब"
      noReason: "कोई कारण प्रदान नहीं किया गया"
    status:
      pending: "प्रतीक्षा में"
      cooldown: "कूलडाउन"
      canAppealAgain: "आप अपील कर सकते हैं"
      canAppeal: "आप अपील कर सकते हैं"
    fields:
      date: "तारीख:"
      reason: "कारण:"
    errors:
      recordFailed: "निर्णय रिकॉर्ड करने में असफल: {error}"
      notFoundOrDeleted: "अपील नहीं मिली या हटाई जा चुकी है।"
      updateFailed: "अपील अपडेट करने में असफल। कृपया बाद में प्रयास करें।"
    dm:
      accepted: "मॉडरेशन कार्रवाई {hubName} के संबंध में आपकी अपील स्वीकार कर ली गई है। हमारी टीम ने आपका मामला समीक्षा किया और निर्णय लिया कि आपकी अपील सही है।"
      declined: "मॉडरेशन कार्रवाई {hubName} के संबंध में आपकी अपील की समीक्षा की गई। सावधानीपूर्वक विचार करने के बाद, आपकी अपील अस्वीकार कर दी गई।"
      moderatorNote: "मॉडरेटर नोट: {reason}"
    embed:
      title: "आपकी अपील योग्य उल्लंघन"
      description: "अपील सबमिट करने के लिए नीचे उल्लंघन चुनें, या मौजूदा अपील की स्थिति देखें।"
      noInfractions:
        title: "साफ सुथरा!"
        description: "कोई अपील योग्य उल्लंघन नहीं मिला।"
      footer:
        canAppeal: "💡 आप {count} उल्लंघन(ओं) पर अपील कर सकते हैं। नीचे सेलेक्ट मेन्यू का उपयोग करें।"
        checkLater: "💡 जब कूलडाउन समाप्त हो या अपील की समीक्षा हो जाए, तब फिर से जाँच करें।"
  errors:
    errorTitle: "त्रुटि!"
    interactionCheck: "आप इस इंटरैक्शन का उपयोग नहीं कर सकते क्योंकि आपने इसे सक्रिय नहीं किया।"
    rateLimited: "आपको रेट लिमिट किया गया है। थोड़ा शांत हो जाएँ।"
    webhookRateLimit: "आपने वेबहुक निर्माण की सीमा को पार कर लिया है।"
    invalidInput: "आपने वैध इनपुट प्रदान नहीं किया है।"
    invalidInvite: "यह आमंत्रण अमान्य है, या समाप्त हो गया है।"
    webhookError: "वेबहुक बनाने में असफल।"
    notConnected: "मैं इस चैनल में कोई हब कनेक्शन नहीं ढूंढ सका।"
    noInteraction: "यह कमांड केवल स्लैश कमांड का समर्थन करता है।"
    missingAppealReference: "अपील संदर्भ गायब है।"
    whoops: "अरे! कुछ गलती हो गई है। कृपया बाद में फिर से प्रयास करें।"
    missingArgument: "आवश्यक तर्क गायब: `{param}`।"
    notConnectedServer: "{cross} इस सर्वर के लिए कोई कनेक्शन नहीं मिला।"
  moderation:
    permissions:
      managerRequired: "Manager+ अनुमतियाँ आवश्यक हैं।"
    target:
      both: "कृपया केवल उपयोगकर्ता या सर्वर निर्दिष्ट करें, दोनों नहीं।"
      missing: "कृपया उपयोगकर्ता या सर्वर निर्दिष्ट करें।"
    revoke:
      noActive: "कोई सक्रिय {action} नहीं मिला।"
      success: "{action} रद्द कर दिया गया।"
    delete:
      noMessage: "हटाने के लिए कोई संदेश प्रदान नहीं किया गया।"
      success: "संदेश सभी कनेक्टेड हब्स से हटा दिया गया।"
      notInterChatMessage: "यह संदेश InterChat संदेश नहीं है या पहले ही हटा दिया गया है।"
      failed: "संदेश हटाने में विफल। कृपया बाद में पुनः प्रयास करें।"
      notFound: "उल्लंघन नहीं मिला।"
    blacklist:
      permissionDenied: "वैश्विक ब्लैकलिस्ट जारी करने के लिए आपके पास InterChat स्टाफ अनुमतियाँ नहीं हैं।"
      alreadyActive: "लक्ष्य पर पहले से ही सक्रिय वैश्विक ब्लैकलिस्ट है।"
      success: "वैश्विक रूप से {target} ब्लैकलिस्ट किया गया।"
    success:
      action: '{target} को {action} {prep} {hubName}'
    errors:
      selectedHubNotFound: "चयनित हब नहीं मिला।"
      processingFailed: "मॉडरेशन कार्रवाई प्रक्रिया में असफल: {error}"
      unknownAction: "अज्ञात कार्रवाई।"
      unsupportedAction: "समर्थित नहीं क्रिया मार्ग।"
      openPanelFailed: "मॉड पैनल खोलने में अप्रत्याशित त्रुटि। कृपया बाद में पुनः प्रयास करें या समर्थन सर्वर में स्टाफ को रिपोर्ट करें।"
      notModeratorForHub: "आप इस हब के लिए मॉडरेटर नहीं हैं।"
      alreadyState: "{targetType} पहले से ही इस हब में {state} है।"
      invalidHubData: "अमान्य हब डेटा।"
      originalMessageNotFound: "मूल संदेश डेटाबेस में नहीं मिला।"
      fetchAuthorOrServerFailed: "संदेश लेखक या सर्वर को लाने में असफल।"
      hubNotFoundForMessage: "इस संदेश के लिए हब नहीं मिला।"
      noModeratedHubs: "आपके पास किसी भी हब में मॉडरेशन अनुमतियाँ नहीं हैं।"
      noTarget: "कृपया एक लक्ष्य निर्दिष्ट करें (उपयोगकर्ता/सर्वर) या संदेश का जवाब दें।"
  infractions:
    errors:
      noPermission: "इस हब के उल्लंघनों को देखने की अनुमति नहीं है।"
      bothSelection: "कृपया केवल उपयोगकर्ता या सर्वर चुनें, दोनों नहीं।"
      invalidServerId: "अमान्य सर्वर आईडी।"
    permissions:
      insufficient: "इस हब में आपको {permission}+ अनुमतियाँ चाहिए।"
      managerRequired: "Manager+ अनुमतियाँ आवश्यक हैं।"
    target:
      both: "कृपया केवल उपयोगकर्ता या सर्वर निर्दिष्ट करें, दोनों नहीं।"
      missing: "कृपया उपयोगकर्ता या सर्वर निर्दिष्ट करें।"
    success:
      action: "{action}{target}{prep}{hubName}"
    revoke:
      noActive: "कोई सक्रिय {action} नहीं मिला।"
      success: "{action} रद्द कर दिया गया।"
    delete:
      notImplemented: "संदेश हटाना अभी लागू नहीं हुआ। कारण रिकॉर्ड किया गया: {reason}"
      notFound: "उल्लंघन नहीं मिला।"
      success: "उल्लंघन हटा दिया गया।"
    blacklist:
      permissionDenied: "वैश्विक ब्लैकलिस्ट जारी करने के लिए आपके पास InterChat स्टाफ अनुमतियाँ नहीं हैं।"
      alreadyActive: "लक्ष्य पर पहले से ही सक्रिय वैश्विक ब्लैकलिस्ट है।"
      success: "वैश्विक रूप से {target} ब्लैकलिस्ट किया गया।"
  report:
    errors:
      processingFailed: "रिपोर्ट प्रक्रिया में त्रुटि! कृपया बाद में पुनः प्रयास करें।"
      notFoundOrDeleted: "रिपोर्ट नहीं मिली या हटाई जा चुकी है।"
      alreadyHandled: "यह रिपोर्ट पहले ही {status} है।"
      updateFailed: "रिपोर्ट अपडेट करने में असफल। कृपया बाद में प्रयास करें।"
    success:
      actionPast: "रिपोर्ट {action}।"
    dm:
      resolved: "आपकी रिपोर्ट के लिए धन्यवाद। हमारी मॉडरेशन टीम ने इसे समीक्षा की और उचित कार्रवाई की। उपयोगकर्ता की गोपनीयता बनाए रखने के लिए हम विशिष्ट विवरण साझा नहीं कर सकते।"
  welcome:
    onGuildJoinTitle: "👋 नमस्ते!"
    onGuildJoinDescription: |
      **मैं InterChat हूँ, और हमारी टीम की ओर से आपके सर्वर पर आने पर प्रसन्न हूँ।**
      हम साथ में आपके सर्वर को Discord के अन्य अद्भुत कम्युनिटीज़ से जोड़ सकते हैं। यहाँ एक जगह में कई, संभावित रूप से हजारों सर्वर, सभी लोगों से भरे हुए हैं जो आपसे बातचीत करने के लिए उत्सुक हैं। 🚀

      **हमारे साथ नए पुल बनाने के लिए तैयार?**
      {dot} **यहाँ नए हैं?** `/setup` कमांड स्टेप-बाय-स्टेप मार्गदर्शन प्रदान करता है।
      {dot} **खोज शुरू करने के लिए तैयार?** हमारे [डिस्कवरी पेज](https://interchat.tech/hubs) पर जाएँ।
      {dot} **कुछ और परिचित लगता है?** एक-से-एक कनेक्शन के लिए `/call` का प्रयास करें।

      💝 **खो गए? सहायता चाहिए?** हमारे [सपोर्ट कम्युनिटी](https://discord.gg/8DhUA4HNpD) में आपका स्वागत है। हमारी टीम और समुदाय आपकी सहायता के लिए तैयार हैं।
  hubEvents:
    serverJoined:
      title: "🎉 नया सर्वर जुड़ा!"
      description: "**{serverName}** हब में शामिल हो गया! चलिए उन्हें समुदाय में गर्मजोशी से स्वागत करें।"
      footer: "स्वागत है {hubName} में!"
  staff:
    blacklist:
      notFound: "कोई मिलती-जुलती ब्लैकलिस्ट प्रविष्टि नहीं मिली।"
      removed: "ब्लैकलिस्ट प्रविष्टि हटा दी गई।"
  user:
    achievements:
      placeholder: "यहाँ उपलब्धियां दिखेंगी"
