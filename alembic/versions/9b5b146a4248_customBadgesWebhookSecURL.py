"""hub blocklist

Revision ID: 9b5b146a4248
Revises: 5c91051fceb2
Create Date: 2025-09-27 20:48:40.289688

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '9b5b146a4248'
down_revision: Union[str, Sequence[str], None] = '5c91051fceb2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('Connection', sa.Column('webhookSecondaryURL', sa.Text(), server_default=sa.text('NULL'), nullable=True))
    op.alter_column('Hub', 'customBadges',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('Hub', 'customBadges',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=postgresql.JSON(astext_type=sa.Text()),
               existing_nullable=True)
    op.drop_column('Connection', 'webhookSecondaryURL')
    # ### end Alembic commands ###
