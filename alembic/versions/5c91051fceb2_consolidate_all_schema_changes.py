"""consolidate_all_schema_changes

Revision ID: 5c91051fceb2
Revises: 18be59152743
Create Date: 2025-09-24 22:58:42.314339

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '5c91051fceb2'
down_revision: Union[str, Sequence[str], None] = '18be59152743'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ServerBlocklist',
    sa.Column('id', sa.Text(), nullable=False),
    sa.Column('serverId', sa.Text(), nullable=False),
    sa.Column('blockedUserId', sa.Text(), nullable=True),
    sa.Column('blockedServerId', sa.Text(), nullable=True),
    sa.Column('reason', sa.Text(), nullable=True),
    sa.Column('createdAt', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updatedAt', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.CheckConstraint('"blockedServerId" IS NULL OR "blockedServerId" != "serverId"', name='check_no_self_block'),
    sa.CheckConstraint('("blockedUserId" IS NOT NULL AND "blockedServerId" IS NULL) OR ("blockedUserId" IS NULL AND "blockedServerId" IS NOT NULL)', name='check_only_one_blocked'),
    sa.ForeignKeyConstraint(['blockedServerId'], ['ServerData.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['blockedUserId'], ['User.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['serverId'], ['ServerData.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('serverId', 'blockedServerId'),
    sa.UniqueConstraint('serverId', 'blockedUserId')
    )
    op.create_index('ServerBlocklist_blockedServerId_idx', 'ServerBlocklist', ['blockedServerId'], unique=False)
    op.create_index('ServerBlocklist_blockedUserId_idx', 'ServerBlocklist', ['blockedUserId'], unique=False)
    op.create_index('ServerBlocklist_serverId_idx', 'ServerBlocklist', ['serverId'], unique=False)
    op.alter_column('Account', 'userId',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('Account', 'type',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('Account', 'provider',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('Account', 'providerAccountId',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('Account', 'refresh_token',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('Account', 'access_token',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('Account', 'token_type',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('Account', 'scope',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('Account', 'id_token',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('Account', 'session_state',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('Account', 'createdAt',
               existing_type=postgresql.TIMESTAMP(precision=3),
               type_=sa.DateTime(timezone=True),
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('Account', 'updatedAt',
               existing_type=postgresql.TIMESTAMP(precision=3),
               type_=sa.DateTime(timezone=True),
               existing_nullable=False)
    op.drop_constraint('Account_userId_fkey', 'Account', type_='foreignkey')
    op.create_foreign_key(None, 'Account', 'User', ['userId'], ['id'], ondelete='CASCADE')

    # --- FIX for AntiSwearRule.actions ---
    op.execute('UPDATE "AntiSwearRule" SET actions = \'{}\' WHERE actions IS NULL')
    op.alter_column('AntiSwearRule', 'actions',
               existing_type=postgresql.ARRAY(postgresql.ENUM('BLOCK_MESSAGE', 'BLACKLIST', 'SEND_ALERT', 'WARN', 'MUTE', 'BAN', name='BlockWordAction')),
               nullable=False,
               existing_server_default=sa.text('ARRAY[]::"BlockWordAction"[]'))

    op.drop_index('AntiSwearRule_hubId_name_key', table_name='AntiSwearRule')
    op.create_unique_constraint(None, 'AntiSwearRule', ['hubId', 'name'])
    op.drop_index('AntiSwearWhitelist_ruleId_word_key', table_name='AntiSwearWhitelist')
    op.create_unique_constraint(None, 'AntiSwearWhitelist', ['ruleId', 'word'])
    op.drop_index('Blacklist_userId_expiresAt_idx', table_name='Blacklist')

    # --- FIX for BlockWord.actions ---
    op.execute('UPDATE "BlockWord" SET actions = \'{}\' WHERE actions IS NULL')
    op.alter_column('BlockWord', 'actions',
               existing_type=postgresql.ARRAY(postgresql.ENUM('BLOCK_MESSAGE', 'BLACKLIST', 'SEND_ALERT', 'WARN', 'MUTE', 'BAN', name='BlockWordAction')),
               nullable=False)

    op.drop_index('BlockWord_hubId_name_key', table_name='BlockWord')
    op.create_unique_constraint(None, 'BlockWord', ['hubId', 'name'])
    op.add_column('Connection', sa.Column('webhookSecondaryURL', sa.Text(), server_default=sa.text('NULL'), nullable=True))
    op.drop_index('Connection_channelId_key', table_name='Connection')
    op.drop_index('Connection_channelId_serverId_key', table_name='Connection')
    op.drop_index('Connection_hubId_serverId_key', table_name='Connection')
    op.create_index('Connection_channel_connected_partial_idx', 'Connection', ['channelId', 'connected'], unique=False, postgresql_where=sa.text('connected = true'))
    op.create_index('Connection_hub_connected_partial_idx', 'Connection', ['hubId'], unique=False, postgresql_where=sa.text('connected = true'))
    op.create_index('Connection_hub_lookup_partial_idx', 'Connection', ['channelId', 'hubId', 'serverId'], unique=False, postgresql_where=sa.text('connected = true'))
    op.create_unique_constraint(None, 'Connection', ['hubId', 'serverId'])
    op.create_unique_constraint(None, 'Connection', ['channelId'])
    op.create_unique_constraint(None, 'Connection', ['channelId', 'serverId'])

    # --- FIX for Hub.customBadges (from previous error) ---
    op.alter_column('Hub', 'customBadges',
               existing_type=sa.TEXT(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True,
               postgresql_using='"customBadges"::jsonb')

    # --- FIX for Hub.rules ---
    op.execute('UPDATE "Hub" SET rules = \'{}\' WHERE rules IS NULL')
    op.alter_column('Hub', 'rules',
               existing_type=postgresql.ARRAY(sa.TEXT()),
               nullable=False)

    op.drop_index('Hub_name_key', table_name='Hub')
    op.create_unique_constraint(None, 'Hub', ['name'])
    op.drop_index('HubActivityMetrics_hubId_key', table_name='HubActivityMetrics')
    op.create_unique_constraint(None, 'HubActivityMetrics', ['hubId'])
    op.drop_index('HubInvite_code_key', table_name='HubInvite')
    op.create_unique_constraint(None, 'HubInvite', ['code'])
    op.drop_index('HubLogConfig_hubId_key', table_name='HubLogConfig')
    op.create_unique_constraint(None, 'HubLogConfig', ['hubId'])
    op.alter_column('HubMessageReaction', 'id',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)

    # --- FIX for HubMessageReaction.users ---
    op.execute('UPDATE "HubMessageReaction" SET users = \'{}\' WHERE users IS NULL')
    op.alter_column('HubMessageReaction', 'users',
               existing_type=postgresql.ARRAY(sa.TEXT()),
               nullable=False)

    op.drop_index('HubMessageReaction_messageId_emoji_key', table_name='HubMessageReaction')
    op.create_unique_constraint(None, 'HubMessageReaction', ['messageId', 'emoji'])
    op.drop_index('HubModerator_hubId_userId_key', table_name='HubModerator')
    op.create_unique_constraint(None, 'HubModerator', ['hubId', 'userId'])
    op.drop_index('HubReview_hubId_userId_key', table_name='HubReview')
    op.create_unique_constraint(None, 'HubReview', ['hubId', 'userId'])
    op.drop_index('HubRulesAcceptance_userId_hubId_key', table_name='HubRulesAcceptance')
    op.create_unique_constraint(None, 'HubRulesAcceptance', ['userId', 'hubId'])
    op.drop_index('HubUpvote_hubId_userId_key', table_name='HubUpvote')
    op.create_unique_constraint(None, 'HubUpvote', ['hubId', 'userId'])
    op.alter_column('Message', 'content',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('Message', 'imageUrl',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('Message', 'channelId',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('Message', 'guildId',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('Message', 'authorId',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.drop_index('Message_guildId_authorId_idx', table_name='Message')
    op.drop_index('ServerBlacklist_serverId_expiresAt_idx', table_name='ServerBlacklist')
    op.alter_column('Session', 'sessionToken',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('Session', 'userId',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('Session', 'expires',
               existing_type=postgresql.TIMESTAMP(precision=3),
               type_=sa.DateTime(timezone=True),
               existing_nullable=False)
    op.alter_column('Session', 'createdAt',
               existing_type=postgresql.TIMESTAMP(precision=3),
               type_=sa.DateTime(timezone=True),
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('Session', 'updatedAt',
               existing_type=postgresql.TIMESTAMP(precision=3),
               type_=sa.DateTime(timezone=True),
               existing_nullable=False)
    op.drop_index('Session_sessionToken_key', table_name='Session')
    op.create_unique_constraint(None, 'Session', ['sessionToken'])
    op.drop_constraint('Session_userId_fkey', 'Session', type_='foreignkey')
    op.create_foreign_key(None, 'Session', 'User', ['userId'], ['id'], ondelete='CASCADE')
    op.drop_index('Tag_name_key', table_name='Tag')
    op.create_unique_constraint(None, 'Tag', ['name'])

    # --- FIX for User.badges ---
    op.execute('UPDATE "User" SET badges = \'{}\' WHERE badges IS NULL')
    op.alter_column('User', 'badges',
               existing_type=postgresql.ARRAY(postgresql.ENUM('VOTER', 'SUPPORTER', 'TRANSLATOR', 'DEVELOPER', 'STAFF', 'BETA_TESTER', name='Badges')),
               nullable=False,
               existing_server_default=sa.text('ARRAY[]::"Badges"[]'))

    # --- FIX for User.preferredLanguages ---
    op.execute('UPDATE "User" SET "preferredLanguages" = \'{}\' WHERE "preferredLanguages" IS NULL')
    op.alter_column('User', 'preferredLanguages',
               existing_type=postgresql.ARRAY(sa.TEXT()),
               nullable=False,
               existing_server_default=sa.text('ARRAY[]::text[]'))

    op.drop_index('UserAchievement_userId_achievementId_key', table_name='UserAchievement')
    op.create_unique_constraint(None, 'UserAchievement', ['userId', 'achievementId'])
    op.create_index('_HubToTag_AB_unique', '_HubToTag', ['A', 'B'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('_HubToTag_AB_unique', table_name='_HubToTag')
    op.drop_constraint(None, 'UserAchievement', type_='unique')
    op.create_index('UserAchievement_userId_achievementId_key', 'UserAchievement', ['userId', 'achievementId'], unique=True)
    op.alter_column('User', 'preferredLanguages',
               existing_type=postgresql.ARRAY(sa.TEXT()),
               nullable=True,
               existing_server_default=sa.text('ARRAY[]::text[]'))
    op.alter_column('User', 'badges',
               existing_type=postgresql.ARRAY(postgresql.ENUM('VOTER', 'SUPPORTER', 'TRANSLATOR', 'DEVELOPER', 'STAFF', 'BETA_TESTER', name='Badges')),
               nullable=True,
               existing_server_default=sa.text('ARRAY[]::"Badges"[]'))
    op.drop_constraint(None, 'Tag', type_='unique')
    op.create_index('Tag_name_key', 'Tag', ['name'], unique=True)
    op.drop_constraint(None, 'Session', type_='foreignkey')
    op.create_foreign_key('Session_userId_fkey', 'Session', 'User', ['userId'], ['id'], onupdate='CASCADE', ondelete='CASCADE')
    op.drop_constraint(None, 'Session', type_='unique')
    op.create_index('Session_sessionToken_key', 'Session', ['sessionToken'], unique=True)
    op.alter_column('Session', 'updatedAt',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(precision=3),
               existing_nullable=False)
    op.alter_column('Session', 'createdAt',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(precision=3),
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('Session', 'expires',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(precision=3),
               existing_nullable=False)
    op.alter_column('Session', 'userId',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('Session', 'sessionToken',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.create_index('ServerBlacklist_serverId_expiresAt_idx', 'ServerBlacklist', ['serverId', 'expiresAt'], unique=False)
    op.create_index('Message_guildId_authorId_idx', 'Message', ['guildId', 'authorId'], unique=False)
    op.alter_column('Message', 'authorId',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('Message', 'guildId',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('Message', 'channelId',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('Message', 'imageUrl',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('Message', 'content',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.drop_constraint(None, 'HubUpvote', type_='unique')
    op.create_index('HubUpvote_hubId_userId_key', 'HubUpvote', ['hubId', 'userId'], unique=True)
    op.drop_constraint(None, 'HubRulesAcceptance', type_='unique')
    op.create_index('HubRulesAcceptance_userId_hubId_key', 'HubRulesAcceptance', ['userId', 'hubId'], unique=True)
    op.drop_constraint(None, 'HubReview', type_='unique')
    op.create_index('HubReview_hubId_userId_key', 'HubReview', ['hubId', 'userId'], unique=True)
    op.drop_constraint(None, 'HubModerator', type_='unique')
    op.create_index('HubModerator_hubId_userId_key', 'HubModerator', ['hubId', 'userId'], unique=True)
    op.drop_constraint(None, 'HubMessageReaction', type_='unique')
    op.create_index('HubMessageReaction_messageId_emoji_key', 'HubMessageReaction', ['messageId', 'emoji'], unique=True)
    op.alter_column('HubMessageReaction', 'users',
               existing_type=postgresql.ARRAY(sa.TEXT()),
               nullable=True)
    op.alter_column('HubMessageReaction', 'id',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.drop_constraint(None, 'HubLogConfig', type_='unique')
    op.create_index('HubLogConfig_hubId_key', 'HubLogConfig', ['hubId'], unique=True)
    op.drop_constraint(None, 'HubInvite', type_='unique')
    op.create_index('HubInvite_code_key', 'HubInvite', ['code'], unique=True)
    op.drop_constraint(None, 'HubActivityMetrics', type_='unique')
    op.create_index('HubActivityMetrics_hubId_key', 'HubActivityMetrics', ['hubId'], unique=True)
    op.drop_constraint(None, 'Hub', type_='unique')
    op.create_index('Hub_name_key', 'Hub', ['name'], unique=True)
    op.alter_column('Hub', 'rules',
               existing_type=postgresql.ARRAY(sa.TEXT()),
               nullable=True)
    op.alter_column('Hub', 'customBadges',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.drop_constraint(None, 'Connection', type_='unique')
    op.drop_constraint(None, 'Connection', type_='unique')
    op.drop_constraint(None, 'Connection', type_='unique')
    op.drop_index('Connection_hub_lookup_partial_idx', table_name='Connection', postgresql_where=sa.text('connected = true'))
    op.drop_index('Connection_hub_connected_partial_idx', table_name='Connection', postgresql_where=sa.text('connected = true'))
    op.drop_index('Connection_channel_connected_partial_idx', table_name='Connection', postgresql_where=sa.text('connected = true'))
    op.create_index('Connection_hubId_serverId_key', 'Connection', ['hubId', 'serverId'], unique=True)
    op.create_index('Connection_channelId_serverId_key', 'Connection', ['channelId', 'serverId'], unique=True)
    op.create_index('Connection_channelId_key', 'Connection', ['channelId'], unique=True)
    op.drop_column('Connection', 'webhookSecondaryURL')
    op.drop_constraint(None, 'BlockWord', type_='unique')
    op.create_index('BlockWord_hubId_name_key', 'BlockWord', ['hubId', 'name'], unique=True)
    op.alter_column('BlockWord', 'actions',
               existing_type=postgresql.ARRAY(postgresql.ENUM('BLOCK_MESSAGE', 'BLACKLIST', 'SEND_ALERT', 'WARN', 'MUTE', 'BAN', name='BlockWordAction')),
               nullable=True)
    op.create_index('Blacklist_userId_expiresAt_idx', 'Blacklist', ['userId', 'expiresAt'], unique=False)
    op.drop_constraint(None, 'AntiSwearWhitelist', type_='unique')
    op.create_index('AntiSwearWhitelist_ruleId_word_key', 'AntiSwearWhitelist', ['ruleId', 'word'], unique=True)
    op.drop_constraint(None, 'AntiSwearRule', type_='unique')
    op.create_index('AntiSwearRule_hubId_name_key', 'AntiSwearRule', ['hubId', 'name'], unique=True)
    op.alter_column('AntiSwearRule', 'actions',
               existing_type=postgresql.ARRAY(postgresql.ENUM('BLOCK_MESSAGE', 'BLACKLIST', 'SEND_ALERT', 'WARN', 'MUTE', 'BAN', name='BlockWordAction')),
               nullable=True,
               existing_server_default=sa.text('ARRAY[]::"BlockWordAction"[]'))
    op.drop_constraint(None, 'Account', type_='foreignkey')
    op.create_foreign_key('Account_userId_fkey', 'Account', 'User', ['userId'], ['id'], onupdate='CASCADE', ondelete='CASCADE')
    op.alter_column('Account', 'updatedAt',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(precision=3),
               existing_nullable=False)
    op.alter_column('Account', 'createdAt',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(precision=3),
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('Account', 'session_state',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('Account', 'id_token',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('Account', 'scope',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('Account', 'token_type',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('Account', 'access_token',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('Account', 'refresh_token',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('Account', 'providerAccountId',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('Account', 'provider',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('Account', 'type',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('Account', 'userId',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.drop_index('ServerBlocklist_serverId_idx', table_name='ServerBlocklist')
    op.drop_index('ServerBlocklist_blockedUserId_idx', table_name='ServerBlocklist')
    op.drop_index('ServerBlocklist_blockedServerId_idx', table_name='ServerBlocklist')
    op.drop_table('ServerBlocklist')
    # ### end Alembic commands ###
