from typing import TYPE_CHECKING

import discord

from utils.modules.common.cogs import CogBase, context_menu
from utils.modules.core.i18n import t
from utils.modules.core.moderation import (
    get_user_moderated_hubs,
    mod_panel_embed,
)
from utils.modules.services.hubService import HubService
from utils.modules.moderation.ui.views import ModPanelView, HubSelectionView
from utils.modules.common.embeds import CommonErrors
from utils.modules.common.database import DatabaseUtils

if TYPE_CHECKING:
    from main import Bot


class ModPanel(CogBase):
    @context_menu('Mod Panel')
    async def mod_panel_message(self, interaction: discord.Interaction['Bot'], message: discord.Message):
        try:
            await interaction.response.defer(ephemeral=True)
            user_locale = await self.get_locale(interaction)

            # Check if user has moderation permissions
            user_hubs = await get_user_moderated_hubs(self.bot, str(interaction.user.id))
            if not user_hubs:
                embed = CommonErrors.no_moderated_hubs(self.bot, user_locale)
                return await interaction.followup.send(embed=embed, ephemeral=True)

            # Try to fetch the original InterChat message
            result = await DatabaseUtils.get_message_with_context(str(message.id))

            if not result:
                embed = CommonErrors.original_message_not_found(self.bot, user_locale)
                return await interaction.followup.send(embed=embed, ephemeral=True)

            original_message, _, hub_id = result

            # Fetch target user and server
            try:
                target_user = await self.bot.fetch_user(int(original_message.authorId))
                target_server = await self.bot.fetch_guild(int(original_message.guildId))
            except (discord.NotFound, ValueError):
                embed = CommonErrors.fetch_author_or_server_failed(self.bot, user_locale)
                return await interaction.followup.send(embed=embed, ephemeral=True)

            # Get the hub
            async with self.bot.db.get_session() as session:
                hub_service = HubService(session)
                selected_hub = await hub_service.get_hub_by_id(hub_id)
            if not selected_hub:
                embed = CommonErrors.hub_not_found_for_message(self.bot, user_locale)
                return await interaction.followup.send(embed=embed, ephemeral=True)

            # Check if user can moderate this hub
            if selected_hub not in user_hubs:
                embed = CommonErrors.not_moderator_for_hub(self.bot, user_locale)
                return await interaction.followup.send(embed=embed, ephemeral=True)

            # Show the mod panel
            view = ModPanelView(
                self.bot,
                interaction.user,
                target_user,
                target_server,
                message,
                selected_hub,
                user_locale,
            )

            embed = mod_panel_embed(
                self.bot,
                selected_hub,
                target_user,
                target_server,
                message,
                original_message.content,
                user_infractions=0,  # TODO: Fetch actual infractions
                server_infractions=0,  # TODO: Fetch actual infractions
                _locale=user_locale,
            )

            await interaction.followup.send(embed=embed, view=view, ephemeral=True)

        except Exception as e:
            raise Exception('Error in mod_panel_message') from e

    @context_menu('Mod Panel', type_=discord.AppCommandType.user)
    async def mod_panel_user(self, interaction: discord.Interaction, user: discord.User):
        """Open moderation panel for a user via context menu."""
        try:
            await interaction.response.defer(ephemeral=True)
            user_locale = await self.get_locale(interaction)

            # Check if user has moderation permissions
            user_hubs = await get_user_moderated_hubs(self.bot, str(interaction.user.id))
            if not user_hubs:
                embed = CommonErrors.no_moderated_hubs(self.bot, user_locale)
                return await interaction.followup.send(embed=embed, ephemeral=True)

            # Get the server (guild) where the command was used
            target_server = interaction.guild

            # Show hub selection since we need to choose which hub to moderate in
            view = HubSelectionView(
                self.bot,
                interaction.user,
                user,
                target_server,
                None,  # No target message
                user_hubs,
                user_locale,
            )

            embed = discord.Embed(
                title=t('ui.moderation.hubSelection.title', locale=user_locale),
                description=t('ui.moderation.hubSelection.description', locale=user_locale),
                color=self.constants.color,
            )

            embed.add_field(
                name=t('ui.moderation.targetSelection.userField', locale=user_locale),
                value=f'{user.mention} (`{user.id}`)',
                inline=True,
            )

            if target_server:
                embed.add_field(
                    name=t('ui.moderation.targetSelection.serverField', locale=user_locale),
                    value=f'**{target_server.name}** (`{target_server.id}`)',
                    inline=True,
                )

            embed.add_field(
                name=t('ui.moderation.hubSelection.fieldHubLabel', locale=user_locale),
                value=t('ui.moderation.hubSelection.fieldHubPrompt', locale=user_locale),
                inline=False,
            )

            await interaction.followup.send(embed=embed, view=view, ephemeral=True)

        except Exception as e:
            raise Exception('Error in mod_panel_user') from e


async def setup(bot: 'Bot'):
    """Load the ModPanel cog."""
    await bot.add_cog(ModPanel(bot))
