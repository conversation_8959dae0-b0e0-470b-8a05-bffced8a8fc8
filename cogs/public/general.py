import discord
from discord.ui import View, Button
from discord.ext import commands
from utils.modules.common.cogs import CogBase

import psutil
import os
import sys

from utils.modules.ui.Localization import create_embed
from utils.modules.core.i18n import t
from utils.utils import load_user_locale

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from main import Bot


class InviteButtons(View):
    def __init__(self, bot, language: str):
        super().__init__()
        self.bot = bot
        self.language = language

        self.add_item(
            Button(
                emoji=bot.emotes.bot_icon,
                label=t('commands.about.buttons.invite', locale=language),
                url='https://discord.com/application-directory/769921109209907241',
                style=discord.ButtonStyle.link,
            )
        )
        self.add_item(
            Button(
                emoji=bot.emotes.code_icon,
                label=t('commands.about.buttons.support', locale=language),
                url='https://discord.gg/8DhUA4HNpD',
                style=discord.ButtonStyle.link,
            )
        )


class AboutButtons(View):
    def __init__(self, bot, locale: str):
        super().__init__()
        self.bot: Bot = bot
        self.locale = locale

        self.add_item(
            Button(
                emoji=bot.emotes.topggSparkles,
                label=t('commands.about.buttons.vote', locale),
                url='https://top.gg/bot/769921109209907241/vote',
                style=discord.ButtonStyle.link,
                row=2,
            )
        )
        self.add_item(
            Button(
                emoji=bot.emotes.bot_icon,
                label=t('commands.about.buttons.invite', locale),
                url='https://discord.com/application-directory/769921109209907241',
                style=discord.ButtonStyle.link,
            )
        )
        self.add_item(
            Button(
                emoji=bot.emotes.wand_icon,
                label=t('commands.about.buttons.dashboard', locale),
                url='https://interchat.tech/dashboard',
                style=discord.ButtonStyle.link,
            )
        )
        self.add_item(
            Button(
                emoji=bot.emotes.code_icon,
                label=t('commands.about.buttons.support', locale),
                url='https://discord.gg/8DhUA4HNpD',
                style=discord.ButtonStyle.link,
            )
        )


class StatButtons(View):
    def __init__(self, bot, language, constants):
        super().__init__()
        self.bot: Bot = bot
        self.language = language
        self.constants = constants

        self.add_item(
            Button(
                emoji=bot.emotes.bot_icon,
                label=t('commands.about.buttons.invite', locale=language),
                url='https://discord.com/application-directory/769921109209907241',
                style=discord.ButtonStyle.link,
            )
        )
        self.add_item(
            Button(
                emoji=bot.emotes.wand_icon,
                label=t('commands.about.buttons.dashboard', locale=language),
                url='https://interchat.tech/dashboard',
                style=discord.ButtonStyle.link,
            )
        )

        shard_info = Button(
            emoji=bot.emotes.gear_icon,
            label=t('commands.about.buttons.shardInfo', locale='en'),
            style=discord.ButtonStyle.grey,
        )
        shard_info.callback = self.shard_info_callback
        self.add_item(shard_info)

    async def shard_info_callback(self, interaction: discord.Interaction):
        await interaction.response.defer()
        language = await load_user_locale(interaction)

        embed = discord.Embed(
            title=t('commands.stats.shard.title', locale=language),
            description=' ',
            color=self.constants.color,
        )

        for shard_id, shard in self.bot.shards.items():
            guild_count = sum(1 for g in self.bot.guilds if g.shard_id == shard_id)
            latency = round(shard.latency * 1000)
            status = (
                t('commands.stats.shard.statusReady', locale=language)
                if latency < 1000
                else t('commands.stats.shard.statusProvisioning', locale=language)
            )

            embed.add_field(
                name=f'Shard #{shard_id}: {status}',
                value=f'```Ping: {latency}ms\nGuilds: {guild_count}```',
                inline=True,
            )

        current_shard = interaction.guild.shard_id if interaction.guild else 0
        embed.set_footer(text=t('commands.stats.shard.current', locale=language, id=current_shard))

        await interaction.followup.send(embed=embed, ephemeral=True)


class General(CogBase):
    def __init__(self, bot):
        self.bot: Bot = bot
        self.constants = self.bot.constants

    @commands.hybrid_command(
        name='about',
        description='🚀 Learn how InterChat helps grow Discord communities',
        extras={'category': 'General'},
    )
    async def about(self, ctx: commands.Context):
        locale = await self.get_locale(ctx)
        view = AboutButtons(self.bot, locale)
        embed = create_embed(
            locale,
            title_key='commands.about.title',
            description_key='commands.about.description_text',
            fields=[
                {
                    'name_key': 'commands.about.features.title',
                    'value_key': 'commands.about.features.list',
                }
            ],
            footer_key='commands.about.support_text',
        )
        await ctx.send(embed=embed, view=view)

    @commands.hybrid_command(
        name='invite', description='Invite the bot to your server!', extras={'category': 'General'}
    )
    async def invite(self, ctx: commands.Context[commands.Bot]):
        language = await self.get_locale(ctx)
        view = InviteButtons(self.bot, language)
        embed = discord.Embed(
            title=t('commands.general.invite.title', locale=language),
            description=t('commands.general.invite.description', locale=language),
            color=self.constants.color,
        )
        await ctx.send(embed=embed, view=view)

    @commands.hybrid_command(
        name='stats', description="📊 View InterChat's statistics.", extras={'category': 'General'}
    )
    async def stats(self, ctx: commands.Context):
        language = await self.get_locale(ctx)
        try:
            py_version = f'{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}'
            process = psutil.Process()
            ram_usage = process.memory_info().rss / (1024**2)
        except Exception:
            py_version = 'N/A'
            ram_usage = 0.0

        embed = discord.Embed(
            title=t('commands.stats.title', locale=language),
            description=' ',
            color=self.constants.color,
        )
        embed.add_field(
            name=f'{self.bot.emotes.bot_icon}  Bot',
            value=f'> **Uptime:** <t:{int(self.bot.start_time.timestamp())}:R>\n> **Servers:** {len(self.bot.guilds)}\n> **Members:** {sum([i.member_count for i in self.bot.guilds if i.member_count])}',
            inline=True,
        )
        embed.add_field(
            name=f'{self.bot.emotes.gear_icon}  System',
            value=f'> **OS:** {sys.platform}\n> **Python:** {py_version}\n> **CPU Cores:** {os.cpu_count()}\n> **RAM Usage:** {ram_usage:.0f}mb',
            inline=True,
        )
        embed.set_footer(text=f'InterChat | Version {self.constants.version}')
        view = StatButtons(self.bot, language, self.constants)
        await ctx.send(embed=embed, view=view)


async def setup(bot):
    await bot.add_cog(General(bot))
