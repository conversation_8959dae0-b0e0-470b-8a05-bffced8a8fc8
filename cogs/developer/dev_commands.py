import os
from typing import TYPE_CHECKING
import discord
from discord.ext import commands
from utils.modules.common.cogs import CogBase

from sqlalchemy.orm.attributes import flag_modified
from sqlalchemy import select

from utils.modules.broadcast.validationService import ValidationService
from utils.modules.broadcast.spamService import SpamService
from utils.modules.core.db.models import Hub

from utils.modules.ui.views.devAnnounce import DeveloperAnnouncement
from utils.modules.core.db.models import Badges
from utils.utils import upsert_user
from utils.modules.ui.layouts.commands.help import HelpLayout

if TYPE_CHECKING:
    from main import Bot


class Developer(CogBase):
    def __init__(self, bot: 'Bot'):
        self.bot = bot

    @commands.hybrid_command(name='dev-announce', description='A developer only command.')
    @commands.is_owner()
    async def announce(self, ctx: commands.Context[commands.Bot]):
        modal = DeveloperAnnouncement(self.bot)
        if not ctx.interaction:
            return
        await ctx.interaction.response.send_modal(modal)

    @commands.command()
    @commands.is_owner()
    async def sync_staff(self, ctx: commands.Context[commands.Bot]):
        await self.bot.sync_staff_ids()
        await ctx.send(f'{self.bot.emotes.tick} Staff IDs synced! {len(self.bot.staff_ids)} staff IDs loaded.')

    @commands.group()
    async def badge(self, ctx: commands.Context[commands.Bot]): ...

    @badge.command()
    @commands.is_owner()
    async def edit(
        self,
        ctx: commands.Context[commands.Bot],
        action: str,
        badge: str,
        user: discord.User,
    ):
        valid_actions = ['add', 'remove']
        valid_badges = ['TRANSLATOR', 'SUPPORTER', 'BETA_TESTER']

        if action.lower() not in valid_actions:
            return

        if badge.upper() not in valid_badges:
            return

        async with self.bot.db.get_session() as session:
            db_user = await upsert_user(user, session)

            current_badges = db_user.badges or []

            if action == 'add' and badge.upper() not in current_badges:
                current_badges.append(Badges[badge.upper()])
                db_user.badges = current_badges
                flag_modified(db_user, 'badges')

            elif action == 'remove' and badge.upper() in current_badges:
                current_badges.remove(Badges[badge.upper()])
                db_user.badges = current_badges
                flag_modified(db_user, 'badges')

            await session.commit()
            await ctx.send('User badges updated!')

    @commands.command(aliases=['v'])
    async def validation(self, ctx: commands.Context[commands.Bot]):
        """Validate a message by replying to it"""

        # Check if this command is a reply to another message
        if not ctx.message.reference or not ctx.message.reference.message_id:
            await ctx.send('Please reply to a message to validate it.')
            return

        try:
            # Get the message being replied to
            message = await ctx.channel.fetch_message(ctx.message.reference.message_id)
        except discord.NotFound:
            await ctx.send('The message you replied to was not found.')
            return
        except discord.Forbidden:
            await ctx.author.send("I don't have permission to fetch that message.")
            return
        except discord.HTTPException:
            await ctx.send('Failed to fetch message due to an error.')
            return

        # Get hub from database
        async with self.bot.db.get_session() as session:
            stmt = select(Hub).where(Hub.id == os.getenv('DEFAULT_HUB_ID', 'obp59geqa3i8ffoku9j60b8d'))
            try:
                hub = (await session.execute(stmt)).scalar_one()
            except Exception as e:
                await ctx.send(f'Failed to fetch hub: {e}')
                return

        # Validate the message
        spam_service = SpamService()
        validator = ValidationService(spam_service)
        try:
            async with self.bot.db.get_session() as session:
                validation_result = await validator.validate_message(
                    message=message,
                    hub=hub,
                    user=message.author,
                    guild=ctx.guild,  # pyright: ignore[reportArgumentType]
                    session=session,
                )

            if validation_result.is_valid:
                await ctx.send('✅ Message is valid.')
            else:
                response = f'❌ Message failed validation: {validation_result.reason}'
                if validation_result.notification_message:
                    response += f'\nNotification: {validation_result.notification_message}'
                await ctx.send(response)

        except Exception as e:
            await ctx.send(f'Validation failed with error: {e}')

    @commands.hybrid_command()
    async def test(self, ctx: commands.Context[commands.Bot]):
        if ctx.interaction:
            await ctx.interaction.response.send_message(view=HelpLayout(self.bot, ctx.author, 'en'))
        else:
            await ctx.send('Ask bread why he did the above line...')


async def setup(bot):
    await bot.add_cog(Developer(bot))
