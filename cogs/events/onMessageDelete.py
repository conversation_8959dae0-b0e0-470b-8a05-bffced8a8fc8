import discord
from discord.ext import commands

from utils.modules.common.cogs import CogBase
from utils.modules.core.messageDelete import delete_interchat_message


class OnMessageDelete(CogBase):
    @commands.Cog.listener()
    async def on_message_delete(self, message: discord.Message):
        if message.author.bot or message.webhook_id:
            return

        await delete_interchat_message(
            bot=self.bot,
            message_id=str(message.id),
            moderator_name=message.author.name,  # For logging who deleted the original
            moderator_id=str(message.author.id),
        )


async def setup(bot):
    await bot.add_cog(OnMessageDelete(bot))
