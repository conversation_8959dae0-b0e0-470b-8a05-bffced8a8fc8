import asyncio
import discord
from discord.ext import commands
from utils.modules.common.cogs import CogBase
from typing import TYPE_CHECKING

from utils.constants import logger
from utils.modules.broadcast.broadcastService import BroadcastService

if TYPE_CHECKING:
    from main import Bot

CONCURRENT_BROADCASTS = 15

class OnMessage(CogBase):
    def __init__(self, bot: 'Bot'):
        self.bot: 'Bot' = bot
        self.broadcast_service = BroadcastService(self.bot)

        self._broadcast_semaphore = asyncio.Semaphore(CONCURRENT_BROADCASTS)
        logger.info(
            f"Broadcast service initialized with a concurrency limit of {CONCURRENT_BROADCASTS} tasks.")

    async def _safe_process_message(self, message: discord.Message):
        """
        A wrapper that acquires the semaphore, processes the message, and ensures
        the semaphore is always released, even if an error occurs.
        """

        async with self._broadcast_semaphore:
            try:
                await self.broadcast_service.process_and_broadcast_message(message)
            except Exception as e:
                # Catching errors here prevents one failed message from crashing the bot.
                logger.error(
                    f'An unexpected error occurred while processing message {message.id} '
                    f'from user {message.author.id}: {e}',
                    exc_info=e,
                )

    @commands.Cog.listener()
    async def on_message(self, message: discord.Message):
        """
        This listener is now extremely fast and non-blocking. It creates an
        independent, managed task for each message.
        """
        if message.author.bot or message.author.system or not message.guild:
            return

        asyncio.create_task(self._safe_process_message(message))


async def setup(bot: 'Bot'):
    await bot.add_cog(OnMessage(bot))
