from typing import TYPE_CHECKING, Optional

import discord
from discord.ext import commands

from utils.modules.core.i18n import t
from utils.modules.moderation.core.base import ModerationBaseCog
from utils.modules.moderation.commands.actions import ModerationActionsCommands
from utils.modules.moderation.commands.panel import ModerationPanelCommands
from utils.modules.moderation.commands.management import ModerationManagementCommands
from utils.modules.ui.AutoComplete import hubm_autocomplete

if TYPE_CHECKING:
    from main import Bot


class Moderation(ModerationBaseCog):
    def __init__(self, bot: 'Bot'):
        super().__init__(bot)

        # command handlers
        self.actions = ModerationActionsCommands(self)
        self.panel_commands = ModerationPanelCommands(self)
        self.management = ModerationManagementCommands(self)

    @hubm_autocomplete
    @commands.hybrid_command(
        name='ban',
        description='Ban a user or server from a hub',
        aliases=['b'],
        extras={'category': 'Hubs'},
    )
    @hubm_autocomplete
    async def ban(
        self,
        ctx: commands.Context['Bot'],
        *,
        reason: str = '',
        user: Optional[discord.User] = None,
        message: discord.Message = None,  # type: ignore
        server: discord.Guild = None,  # type: ignore
        hub: Optional[str] = None,
    ):
        await self.actions.ban(ctx, reason=reason, user=user, message=message, server=server, hub=hub)

    @hubm_autocomplete
    @commands.hybrid_command(
        name='mute',
        description='Mute a user or server from a hub',
        aliases=['m'],
        extras={'category': 'Hubs'},
    )
    @hubm_autocomplete
    async def mute(
        self,
        ctx: commands.Context['Bot'],
        duration: str,
        *,
        reason: str = '',
        user: Optional[discord.User] = None,
        message: discord.Message = None,  # type: ignore
        server: discord.Guild = None,  # type: ignore
        hub: Optional[str] = None,
    ):
        await self.actions.mute(ctx, duration, reason=reason, user=user, message=message, server=server, hub=hub)

    @hubm_autocomplete
    @commands.hybrid_command(
        name='warn',
        description='Warn a user or server in a hub',
        aliases=['w'],
        extras={'category': 'Hubs'},
    )
    @hubm_autocomplete
    async def warn(
        self,
        ctx: commands.Context['Bot'],
        *,
        reason: str = '',
        user: Optional[discord.User] = None,
        message: discord.Message = None,  # type: ignore
        server: discord.Guild = None,  # type: ignore
        hub: Optional[str] = None,
    ):
        await self.actions.warn(ctx, reason=reason, user=user, message=message, server=server, hub=hub)

    @hubm_autocomplete
    @commands.hybrid_command(
        name='unmute',
        description='Unmute a user or server from a hub',
        aliases=['um'],
        extras={'category': 'Hubs'},
    )
    @hubm_autocomplete
    async def unmute(
        self,
        ctx: commands.Context['Bot'],
        *,
        reason: str = '',
        user: Optional[discord.User] = None,
        message: discord.Message = None,  # type: ignore
        server: discord.Guild = None,  # type: ignore
        hub: Optional[str] = None,
    ):
        await self.actions.unmute(ctx, reason=reason, user=user, message=message, server=server, hub=hub)

    @hubm_autocomplete
    @commands.hybrid_command(
        name='unban',
        description='Unban a user or server from a hub',
        aliases=['ub'],
        extras={'category': 'Hubs'},
    )
    @hubm_autocomplete
    async def unban(
        self,
        ctx: commands.Context['Bot'],
        *,
        reason: str = '',
        user: Optional[discord.User] = None,
        message: discord.Message = None,  # type: ignore
        server: discord.Guild = None,  # type: ignore
        hub: Optional[str] = None,
    ):
        await self.actions.unban(ctx, reason=reason, user=user, message=message, server=server, hub=hub)

    @hubm_autocomplete
    @commands.hybrid_command(
        name='modpanel',
        description=t('commands.mod.panel.description', locale='en'),
        aliases=['p'],
        extras={'category': 'Hubs'},
    )
    async def panel(
        self,
        ctx: commands.Context[commands.Bot],
        user: Optional[discord.User] = None,
        message: Optional[discord.Message] = None,
        server: Optional[discord.Guild] = None,
    ):
        await self.panel_commands.panel(ctx, user=user, message=message, server=server)

    @hubm_autocomplete
    @commands.hybrid_command(
        name='delete_infraction',
        description='Delete an infraction (requires Manager+ permissions)',
        extras={'category': 'Hubs'},
    )
    @hubm_autocomplete
    async def delete_infraction(
        self,
        ctx: commands.Context['Bot'],
        hub: Optional[str],
        infraction_id: str,
    ):
        await self.management.delete_infraction(ctx, hub, infraction_id)


async def setup(bot: 'Bot'):
    await bot.add_cog(Moderation(bot))
