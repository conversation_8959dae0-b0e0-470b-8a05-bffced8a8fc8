from utils.modules.core.db.models import Badges


badges = {
    Badges.VOTER: {
        'name': 'Voter',
        'global': True,
        'description': 'Voted for InterChat in the last 12 hours',
        'icon': 'voter_badge',
    },
    Badges.SUPPORTER: {
        'name': 'Supporter',
        'global': True,
        'description': 'Donates to InterChat',
        'icon': 'donator_badge',
    },
    Badges.TRANSLATOR: {
        'name': 'Translator',
        'global': True,
       'description': 'Submit translations to InterChat',
        'icon': 'translator_badge',
    },
    Badges.STAFF: {
        'name': 'InterChat Staff',
        'global': True,
        'description': 'InterChat staff member',
        'icon': 'staff_badge',
    },
    Badges.BETA_TESTER: {
        'name': 'Beta Tester',
        'global': True,
        'description': 'Tested a pre-release version of InterChat',
        'icon': 'beta_tester_badge',
    },
    Badges.HUB_OWNER: {
        'name': 'Hub Owner',
        'global': False,
        'description': 'Owner of this hub',
        'icon': 'hub_owner',
    },
    Badges.HUB_MANAGER: {
        'name': 'Hub Manager',
        'global': False,
        'description': 'Manager of this hub',
        'icon': 'hub_manager',
    },
    Badges.HUB_MODERATOR: {
        'name': 'Hub Moderator',
        'global': False,
        'description': 'Moderator of this hub',
        'icon': 'hub_moderator',
    },
    Badges.TOP_CHATTER: {
        'name': 'Top Chatter',
        'global': False,
        'description': 'Sent the most messages in this hub in the last 24 hours',
        'icon': 'top_Chatter',
    }
}
